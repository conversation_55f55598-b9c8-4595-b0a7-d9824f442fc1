import { message } from "antd";
import React, { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";
import { getUserInfo } from "../apicalls/users";
import { useDispatch, useSelector } from "react-redux";
import { SetUser } from "../redux/usersSlice.js";
import { useNavigate, useLocation } from "react-router-dom";
import { HideLoading, ShowLoading } from "../redux/loaderSlice";
import { checkPaymentStatus } from "../apicalls/payment.js";
import "./ProtectedRoute.css";
import { SetSubscription } from "../redux/subscriptionSlice.js";
import { setPaymentVerificationNeeded } from "../redux/paymentSlice.js";


function ProtectedRoute({ children }) {
  const { user } = useSelector((state) => state.user);
  const [isPaymentPending, setIsPaymentPending] = useState(false);
  const intervalRef = useRef(null);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const { paymentVerificationNeeded } = useSelector((state) => state.payment);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const activeRoute = location.pathname;





  const getUserData = async () => {
    try {
      const response = await getUserInfo();
      if (response.success) {
        dispatch(SetUser(response.data));
      } else {
        message.error(response.message);
        navigate("/login");
      }
    } catch (error) {
      navigate("/login");
      message.error(error.message);
    }
  };

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      getUserData();
    } else {
      navigate("/login");
    }
  }, []);



  useEffect(() => {
    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {
      navigate('/user/plans');
    }
  }, [isPaymentPending, activeRoute, navigate]);

  const verifyPaymentStatus = async () => {
    try {
      const data = await checkPaymentStatus();
      console.log("Payment Status:", data);
      if (data?.error || data?.paymentStatus !== 'paid') {
        if (subscriptionData !== null) {
          dispatch(SetSubscription(null));
        }
        setIsPaymentPending(true);
      }
      else {
        setIsPaymentPending(false);
        dispatch(SetSubscription(data));
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      }
    } catch (error) {
      console.log("Error checking payment status:", error);
      dispatch(SetSubscription(null));
      setIsPaymentPending(true);
    }
  };

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing 2222222...");

      if (paymentVerificationNeeded) {
        console.log('Inside timer in effect 2....');
        intervalRef.current = setInterval(() => {
          console.log('Timer in action...');
          verifyPaymentStatus();
        }, 15000);
        dispatch(setPaymentVerificationNeeded(false));
      }
    }
  }, [paymentVerificationNeeded]);

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing...");
      verifyPaymentStatus();
    }
  }, [user, activeRoute]);


  const getButtonClass = (title) => {
    // Exclude "Plans" and "Profile" buttons from the "button-disabled" class
    if (!user.paymentRequired || title === "Plans" || title === "Profile" || title === "Logout") {
      return ""; // No class applied
    }

    return subscriptionData?.paymentStatus !== "paid" && user?.paymentRequired
      ? "button-disabled"
      : "";
  };




  return (
    <div className="layout-modern min-h-screen flex flex-col">
      {/* No sidebar - users will use hub for navigation */}


      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Modern Responsive Header */}
        <motion.header
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className={`nav-modern ${
            location.pathname.includes('/quiz') || location.pathname.includes('/write-exam')
              ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98'
              : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'
          } backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`}
        >
          <div className="px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10">
            <div className="flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20">
              {/* Left Section - Back Button */}
              <div className="flex items-center flex-shrink-0">
                <button
                  onClick={() => {
                    // If on hub page, go to home, otherwise go back
                    if (location.pathname === '/user/hub') {
                      navigate('/');
                    } else {
                      window.history.back();
                    }
                  }}
                  className="back-button group flex items-center space-x-1 xs:space-x-2 px-3 xs:px-4 sm:px-5 py-2 xs:py-2.5 bg-gradient-to-r from-gray-600 via-gray-500 to-gray-600 hover:from-gray-700 hover:via-gray-600 hover:to-gray-700 text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 shadow-lg hover:shadow-2xl hover:shadow-gray-500/25 text-xs xs:text-sm sm:text-base border border-gray-400/20"
                >
                  <i className="ri-arrow-left-line text-sm xs:text-base sm:text-lg group-hover:-translate-x-1 transition-transform duration-300"></i>
                  <span className="hidden xs:inline tracking-wide">Back</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                </button>
              </div>

              {/* Center Section - Brainwave Heading */}
              <div className="flex-1 flex justify-center px-2 xs:px-4">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="relative group"
                >
                  <h1 className="brainwave-heading-enhanced text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl font-black tracking-tight relative z-10">
                    <span className="brain-text">Brain</span>
                    <span className="wave-text">Wave</span>
                  </h1>
                  <div className="title-glow absolute inset-0 bg-gradient-to-r from-blue-600/30 via-indigo-600/30 to-purple-600/30 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110"></div>
                  <div className="title-shine absolute -inset-2 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10 rounded-lg"></div>
                </motion.div>
              </div>

              {/* Right Section - User Profile */}
              <div className="flex items-center justify-end flex-shrink-0">
                {/* User Profile Avatar */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="user-profile-container group"
                >
                  <div className="user-avatar relative w-10 h-10 xs:w-11 xs:h-11 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-full overflow-hidden flex items-center justify-center border-3 border-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-110 hover:-translate-y-1 cursor-pointer">
                    {user?.profileImage ? (
                      <img
                        src={user.profileImage}
                        alt="Profile"
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 text-white font-bold text-lg xs:text-xl sm:text-2xl lg:text-3xl relative overflow-hidden">
                        <span className="relative z-10">{user?.name?.charAt(0)?.toUpperCase() || 'U'}</span>
                        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                    )}
                    <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-20 blur transition-opacity duration-300"></div>
                    <div className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-400/20 via-indigo-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50 pb-20 sm:pb-0">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="h-full"
          >
            {children}
          </motion.div>
        </main>

        {/* Modern Bottom Navigation - Mobile Only */}
        <motion.nav
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="fixed bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white/98 to-white/95 backdrop-blur-xl border-t border-blue-100/50 shadow-2xl shadow-blue-100/20 sm:hidden z-40"
        >
          <div className="px-4 py-3">
            <div className="flex justify-center">
              <button
                onClick={() => {
                  // If on hub page, go to home, otherwise go back
                  if (location.pathname === '/user/hub') {
                    navigate('/');
                  } else {
                    window.history.back();
                  }
                }}
                className="group flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-gray-600 via-gray-500 to-gray-600 hover:from-gray-700 hover:via-gray-600 hover:to-gray-700 text-white rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl hover:shadow-gray-500/25 border border-gray-400/20 min-w-32 relative overflow-hidden"
              >
                <i className="ri-arrow-left-line text-lg group-hover:-translate-x-1 transition-transform duration-300"></i>
                <span className="tracking-wide">Back</span>
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
              </button>
            </div>
          </div>
        </motion.nav>
      </div>
    </div>
  );
}

export default ProtectedRoute;
