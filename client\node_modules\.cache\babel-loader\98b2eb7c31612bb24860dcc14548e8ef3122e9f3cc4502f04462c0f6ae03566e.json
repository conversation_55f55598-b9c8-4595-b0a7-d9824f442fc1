{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { getUserInfo } from \"../apicalls/users\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { SetUser } from \"../redux/usersSlice.js\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\nimport \"./ProtectedRoute.css\";\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  var _user$name, _user$name$charAt;\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\n  const intervalRef = useRef(null);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const {\n    paymentVerificationNeeded\n  } = useSelector(state => state.payment);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const activeRoute = location.pathname;\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        dispatch(SetUser(response.data));\n      } else {\n        message.error(response.message);\n        navigate(\"/login\");\n      }\n    } catch (error) {\n      navigate(\"/login\");\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      getUserData();\n    } else {\n      navigate(\"/login\");\n    }\n  }, []);\n  useEffect(() => {\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\n      navigate('/user/plans');\n    }\n  }, [isPaymentPending, activeRoute, navigate]);\n  const verifyPaymentStatus = async () => {\n    try {\n      const data = await checkPaymentStatus();\n      console.log(\"Payment Status:\", data);\n      if (data !== null && data !== void 0 && data.error || (data === null || data === void 0 ? void 0 : data.paymentStatus) !== 'paid') {\n        if (subscriptionData !== null) {\n          dispatch(SetSubscription(null));\n        }\n        setIsPaymentPending(true);\n      } else {\n        setIsPaymentPending(false);\n        dispatch(SetSubscription(data));\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n        }\n      }\n    } catch (error) {\n      console.log(\"Error checking payment status:\", error);\n      dispatch(SetSubscription(null));\n      setIsPaymentPending(true);\n    }\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing 2222222...\");\n      if (paymentVerificationNeeded) {\n        console.log('Inside timer in effect 2....');\n        intervalRef.current = setInterval(() => {\n          console.log('Timer in action...');\n          verifyPaymentStatus();\n        }, 15000);\n        dispatch(setPaymentVerificationNeeded(false));\n      }\n    }\n  }, [paymentVerificationNeeded]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing...\");\n      verifyPaymentStatus();\n    }\n  }, [user, activeRoute]);\n  const getButtonClass = title => {\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\n      return \"\"; // No class applied\n    }\n\n    return (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" && user !== null && user !== void 0 && user.paymentRequired ? \"button-disabled\" : \"\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout-modern min-h-screen flex flex-col\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col min-h-screen\",\n      children: [/*#__PURE__*/_jsxDEV(motion.header, {\n        initial: {\n          y: -20,\n          opacity: 0\n        },\n        animate: {\n          y: 0,\n          opacity: 1\n        },\n        className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => window.history.back(),\n                className: \"back-button group flex items-center space-x-1 xs:space-x-2 px-3 xs:px-4 sm:px-5 py-2 xs:py-2.5 bg-gradient-to-r from-gray-600 via-gray-500 to-gray-600 hover:from-gray-700 hover:via-gray-600 hover:to-gray-700 text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 shadow-lg hover:shadow-2xl hover:shadow-gray-500/25 text-xs xs:text-sm sm:text-base border border-gray-400/20\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"ri-arrow-left-line text-sm xs:text-base sm:text-lg group-hover:-translate-x-1 transition-transform duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden xs:inline tracking-wide\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 flex justify-center px-2 xs:px-4\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.6,\n                  delay: 0.2\n                },\n                className: \"relative group\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"brainwave-heading-enhanced text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl font-black tracking-tight relative z-10\",\n                  children: location.pathname === '/user/hub' ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"hub-title-gradient\",\n                    children: \"Study Smarter\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"brain-text\",\n                      children: \"Brain\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"wave-text\",\n                      children: \"Wave\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 164,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"title-glow absolute inset-0 bg-gradient-to-r from-blue-600/30 via-indigo-600/30 to-purple-600/30 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"title-shine absolute -inset-2 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10 rounded-lg\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-end flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.8\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 0.3\n                },\n                className: \"user-profile-container group\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-avatar relative w-10 h-10 xs:w-11 xs:h-11 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-full overflow-hidden flex items-center justify-center border-3 border-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-110 hover:-translate-y-1 cursor-pointer\",\n                  children: [user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: user.profileImage,\n                    alt: \"Profile\",\n                    className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\",\n                    onError: e => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 text-white font-bold text-lg xs:text-xl sm:text-2xl lg:text-3xl relative overflow-hidden\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"relative z-10\",\n                      children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || 'U'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute -inset-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-20 blur transition-opacity duration-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 rounded-full bg-gradient-to-br from-blue-400/20 via-indigo-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50 pb-20 sm:pb-0\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3,\n            delay: 0.1\n          },\n          className: \"h-full\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.nav, {\n        initial: {\n          y: 100,\n          opacity: 0\n        },\n        animate: {\n          y: 0,\n          opacity: 1\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: \"fixed bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white/98 to-white/95 backdrop-blur-xl border-t border-blue-100/50 shadow-2xl shadow-blue-100/20 sm:hidden z-40\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.history.back(),\n              className: \"group flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-gray-600 via-gray-500 to-gray-600 hover:from-gray-700 hover:via-gray-600 hover:to-gray-700 text-white rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl hover:shadow-gray-500/25 border border-gray-400/20 min-w-32 relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"ri-arrow-left-line text-lg group-hover:-translate-x-1 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"tracking-wide\",\n                children: \"Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n}\n_s(ProtectedRoute, \"cwn50ZKVcb7yRGicmnB70bd2a+Y=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch, useNavigate, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useRef", "motion", "getUserInfo", "useDispatch", "useSelector", "SetUser", "useNavigate", "useLocation", "HideLoading", "ShowLoading", "checkPaymentStatus", "SetSubscription", "setPaymentVerificationNeeded", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "_s", "_user$name", "_user$name$charAt", "user", "state", "isPaymentPending", "setIsPaymentPending", "intervalRef", "subscriptionData", "subscription", "paymentVerificationNeeded", "payment", "dispatch", "navigate", "location", "activeRoute", "pathname", "getUserData", "response", "success", "data", "error", "token", "localStorage", "getItem", "includes", "verifyPaymentStatus", "console", "log", "paymentStatus", "current", "clearInterval", "paymentRequired", "isAdmin", "setInterval", "getButtonClass", "title", "className", "header", "initial", "y", "opacity", "animate", "onClick", "window", "history", "back", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "scale", "transition", "duration", "delay", "profileImage", "src", "alt", "onError", "e", "target", "style", "display", "nextS<PERSON>ling", "name", "char<PERSON>t", "toUpperCase", "nav", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\n\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const activeRoute = location.pathname;\r\n\r\n\r\n\r\n\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n      } else {\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (token) {\r\n      getUserData();\r\n    } else {\r\n      navigate(\"/login\");\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\r\n      navigate('/user/plans');\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        setIsPaymentPending(true);\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      setIsPaymentPending(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing 2222222...\");\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    }\r\n  }, [paymentVerificationNeeded]);\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing...\");\r\n      verifyPaymentStatus();\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\r\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    return subscriptionData?.paymentStatus !== \"paid\" && user?.paymentRequired\r\n      ? \"button-disabled\"\r\n      : \"\";\r\n  };\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex flex-col\">\r\n      {/* No sidebar - users will use hub for navigation */}\r\n\r\n\r\n      {/* Main Content Area */}\r\n      <div className=\"flex-1 flex flex-col min-h-screen\">\r\n        {/* Modern Responsive Header */}\r\n        <motion.header\r\n          initial={{ y: -20, opacity: 0 }}\r\n          animate={{ y: 0, opacity: 1 }}\r\n          className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\"\r\n        >\r\n          <div className=\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n            <div className=\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\">\r\n              {/* Left Section - Back Button */}\r\n              <div className=\"flex items-center flex-shrink-0\">\r\n                <button\r\n                  onClick={() => window.history.back()}\r\n                  className=\"back-button group flex items-center space-x-1 xs:space-x-2 px-3 xs:px-4 sm:px-5 py-2 xs:py-2.5 bg-gradient-to-r from-gray-600 via-gray-500 to-gray-600 hover:from-gray-700 hover:via-gray-600 hover:to-gray-700 text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 shadow-lg hover:shadow-2xl hover:shadow-gray-500/25 text-xs xs:text-sm sm:text-base border border-gray-400/20\"\r\n                >\r\n                  <i className=\"ri-arrow-left-line text-sm xs:text-base sm:text-lg group-hover:-translate-x-1 transition-transform duration-300\"></i>\r\n                  <span className=\"hidden xs:inline tracking-wide\">Back</span>\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\"></div>\r\n                </button>\r\n              </div>\r\n\r\n              {/* Center Section - Brainwave Heading */}\r\n              <div className=\"flex-1 flex justify-center px-2 xs:px-4\">\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.9 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.6, delay: 0.2 }}\r\n                  className=\"relative group\"\r\n                >\r\n                  <h1 className=\"brainwave-heading-enhanced text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl font-black tracking-tight relative z-10\">\r\n                    {location.pathname === '/user/hub' ? (\r\n                      <span className=\"hub-title-gradient\">Study Smarter</span>\r\n                    ) : (\r\n                      <>\r\n                        <span className=\"brain-text\">Brain</span>\r\n                        <span className=\"wave-text\">Wave</span>\r\n                      </>\r\n                    )}\r\n                  </h1>\r\n                  <div className=\"title-glow absolute inset-0 bg-gradient-to-r from-blue-600/30 via-indigo-600/30 to-purple-600/30 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n                  <div className=\"title-shine absolute -inset-2 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10 rounded-lg\"></div>\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Right Section - User Profile */}\r\n              <div className=\"flex items-center justify-end flex-shrink-0\">\r\n                {/* User Profile Avatar */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"user-profile-container group\"\r\n                >\r\n                  <div className=\"user-avatar relative w-10 h-10 xs:w-11 xs:h-11 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-full overflow-hidden flex items-center justify-center border-3 border-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-110 hover:-translate-y-1 cursor-pointer\">\r\n                    {user?.profileImage ? (\r\n                      <img\r\n                        src={user.profileImage}\r\n                        alt=\"Profile\"\r\n                        className=\"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\"\r\n                        onError={(e) => {\r\n                          e.target.style.display = 'none';\r\n                          e.target.nextSibling.style.display = 'flex';\r\n                        }}\r\n                      />\r\n                    ) : (\r\n                      <div className=\"w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 text-white font-bold text-lg xs:text-xl sm:text-2xl lg:text-3xl relative overflow-hidden\">\r\n                        <span className=\"relative z-10\">{user?.name?.charAt(0)?.toUpperCase() || 'U'}</span>\r\n                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                      </div>\r\n                    )}\r\n                    <div className=\"absolute -inset-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-20 blur transition-opacity duration-300\"></div>\r\n                    <div className=\"absolute inset-0 rounded-full bg-gradient-to-br from-blue-400/20 via-indigo-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                  </div>\r\n                </motion.div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n\r\n        {/* Page Content */}\r\n        <main className=\"flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50 pb-20 sm:pb-0\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.1 }}\r\n            className=\"h-full\"\r\n          >\r\n            {children}\r\n          </motion.div>\r\n        </main>\r\n\r\n        {/* Modern Bottom Navigation - Mobile Only */}\r\n        <motion.nav\r\n          initial={{ y: 100, opacity: 0 }}\r\n          animate={{ y: 0, opacity: 1 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"fixed bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white/98 to-white/95 backdrop-blur-xl border-t border-blue-100/50 shadow-2xl shadow-blue-100/20 sm:hidden z-40\"\r\n        >\r\n          <div className=\"px-4 py-3\">\r\n            <div className=\"flex justify-center\">\r\n              <button\r\n                onClick={() => window.history.back()}\r\n                className=\"group flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-gray-600 via-gray-500 to-gray-600 hover:from-gray-700 hover:via-gray-600 hover:to-gray-700 text-white rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl hover:shadow-gray-500/25 border border-gray-400/20 min-w-32 relative overflow-hidden\"\r\n              >\r\n                <i className=\"ri-arrow-left-line text-lg group-hover:-translate-x-1 transition-transform duration-300\"></i>\r\n                <span className=\"tracking-wide\">Back</span>\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl\"></div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </motion.nav>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,sBAAsB;AAC/D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAO,sBAAsB;AAC7B,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,4BAA4B,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAGxE,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM2B,WAAW,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM;IAAE2B;EAAiB,CAAC,GAAGvB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACK,YAAY,CAAC;EACvE,MAAM;IAAEC;EAA0B,CAAC,GAAGzB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACO,OAAO,CAAC;EAC3E,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,WAAW,GAAGD,QAAQ,CAACE,QAAQ;EAMrC,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnC,WAAW,CAAC,CAAC;MACpC,IAAImC,QAAQ,CAACC,OAAO,EAAE;QACpBP,QAAQ,CAAC1B,OAAO,CAACgC,QAAQ,CAACE,IAAI,CAAC,CAAC;MAClC,CAAC,MAAM;QACL3C,OAAO,CAAC4C,KAAK,CAACH,QAAQ,CAACzC,OAAO,CAAC;QAC/BoC,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdR,QAAQ,CAAC,QAAQ,CAAC;MAClBpC,OAAO,CAAC4C,KAAK,CAACA,KAAK,CAAC5C,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDE,SAAS,CAAC,MAAM;IACd,MAAM2C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTL,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLJ,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;EAINlC,SAAS,CAAC,MAAM;IACd,IAAI0B,gBAAgB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAACoB,QAAQ,CAACV,WAAW,CAAC,EAAE;MACrEF,QAAQ,CAAC,aAAa,CAAC;IACzB;EACF,CAAC,EAAE,CAACR,gBAAgB,EAAEU,WAAW,EAAEF,QAAQ,CAAC,CAAC;EAE7C,MAAMa,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMN,IAAI,GAAG,MAAM7B,kBAAkB,CAAC,CAAC;MACvCoC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAER,IAAI,CAAC;MACpC,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,KAAK,IAAI,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,aAAa,MAAK,MAAM,EAAE;QACjD,IAAIrB,gBAAgB,KAAK,IAAI,EAAE;UAC7BI,QAAQ,CAACpB,eAAe,CAAC,IAAI,CAAC,CAAC;QACjC;QACAc,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MACI;QACHA,mBAAmB,CAAC,KAAK,CAAC;QAC1BM,QAAQ,CAACpB,eAAe,CAAC4B,IAAI,CAAC,CAAC;QAC/B,IAAIb,WAAW,CAACuB,OAAO,EAAE;UACvBC,aAAa,CAACxB,WAAW,CAACuB,OAAO,CAAC;QACpC;MACF;IACF,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdM,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEP,KAAK,CAAC;MACpDT,QAAQ,CAACpB,eAAe,CAAC,IAAI,CAAC,CAAC;MAC/Bc,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED3B,SAAS,CAAC,MAAM;IACd,IAAIwB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,IAAI,EAAC7B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAE;MAC3CN,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MAEvC,IAAIlB,yBAAyB,EAAE;QAC7BiB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CrB,WAAW,CAACuB,OAAO,GAAGI,WAAW,CAAC,MAAM;UACtCP,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCF,mBAAmB,CAAC,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC;QACTd,QAAQ,CAACnB,4BAA4B,CAAC,KAAK,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,EAAE,CAACiB,yBAAyB,CAAC,CAAC;EAE/B/B,SAAS,CAAC,MAAM;IACd,IAAIwB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,IAAI,EAAC7B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAE;MAC3CN,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BF,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACvB,IAAI,EAAEY,WAAW,CAAC,CAAC;EAGvB,MAAMoB,cAAc,GAAIC,KAAK,IAAK;IAChC;IACA,IAAI,CAACjC,IAAI,CAAC6B,eAAe,IAAII,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,QAAQ,EAAE;MAC3F,OAAO,EAAE,CAAC,CAAC;IACb;;IAEA,OAAO,CAAA5B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqB,aAAa,MAAK,MAAM,IAAI1B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,GACtE,iBAAiB,GACjB,EAAE;EACR,CAAC;EAKD,oBACErC,OAAA;IAAK0C,SAAS,EAAC,0CAA0C;IAAAtC,QAAA,eAKvDJ,OAAA;MAAK0C,SAAS,EAAC,mCAAmC;MAAAtC,QAAA,gBAEhDJ,OAAA,CAACb,MAAM,CAACwD,MAAM;QACZC,OAAO,EAAE;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QAChCC,OAAO,EAAE;UAAEF,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAC9BJ,SAAS,EAAC,kKAAkK;QAAAtC,QAAA,eAE5KJ,OAAA;UAAK0C,SAAS,EAAC,+CAA+C;UAAAtC,QAAA,eAC5DJ,OAAA;YAAK0C,SAAS,EAAC,wEAAwE;YAAAtC,QAAA,gBAErFJ,OAAA;cAAK0C,SAAS,EAAC,iCAAiC;cAAAtC,QAAA,eAC9CJ,OAAA;gBACEgD,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;gBACrCT,SAAS,EAAC,4aAA4a;gBAAAtC,QAAA,gBAEtbJ,OAAA;kBAAG0C,SAAS,EAAC;gBAAiH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnIvD,OAAA;kBAAM0C,SAAS,EAAC,gCAAgC;kBAAAtC,QAAA,EAAC;gBAAI;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5DvD,OAAA;kBAAK0C,SAAS,EAAC;gBAA6I;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7J;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNvD,OAAA;cAAK0C,SAAS,EAAC,yCAAyC;cAAAtC,QAAA,eACtDJ,OAAA,CAACb,MAAM,CAACqE,GAAG;gBACTZ,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAEW,KAAK,EAAE;gBAAI,CAAE;gBACpCV,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEW,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAC1ClB,SAAS,EAAC,gBAAgB;gBAAAtC,QAAA,gBAE1BJ,OAAA;kBAAI0C,SAAS,EAAC,2HAA2H;kBAAAtC,QAAA,EACtIe,QAAQ,CAACE,QAAQ,KAAK,WAAW,gBAChCrB,OAAA;oBAAM0C,SAAS,EAAC,oBAAoB;oBAAAtC,QAAA,EAAC;kBAAa;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,gBAEzDvD,OAAA,CAAAE,SAAA;oBAAAE,QAAA,gBACEJ,OAAA;sBAAM0C,SAAS,EAAC,YAAY;sBAAAtC,QAAA,EAAC;oBAAK;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzCvD,OAAA;sBAAM0C,SAAS,EAAC,WAAW;sBAAAtC,QAAA,EAAC;oBAAI;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACvC;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACLvD,OAAA;kBAAK0C,SAAS,EAAC;gBAAwL;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9MvD,OAAA;kBAAK0C,SAAS,EAAC;gBAAgL;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNvD,OAAA;cAAK0C,SAAS,EAAC,6CAA6C;cAAAtC,QAAA,eAE1DJ,OAAA,CAACb,MAAM,CAACqE,GAAG;gBACTZ,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAEW,KAAK,EAAE;gBAAI,CAAE;gBACpCV,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEW,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAC1ClB,SAAS,EAAC,8BAA8B;gBAAAtC,QAAA,eAExCJ,OAAA;kBAAK0C,SAAS,EAAC,ySAAyS;kBAAAtC,QAAA,GACrTI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqD,YAAY,gBACjB7D,OAAA;oBACE8D,GAAG,EAAEtD,IAAI,CAACqD,YAAa;oBACvBE,GAAG,EAAC,SAAS;oBACbrB,SAAS,EAAC,oFAAoF;oBAC9FsB,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;sBAC/BH,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFvD,OAAA;oBAAK0C,SAAS,EAAC,sMAAsM;oBAAAtC,QAAA,gBACnNJ,OAAA;sBAAM0C,SAAS,EAAC,eAAe;sBAAAtC,QAAA,EAAE,CAAAI,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAE8D,IAAI,cAAAhE,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYiE,MAAM,CAAC,CAAC,CAAC,cAAAhE,iBAAA,uBAArBA,iBAAA,CAAuBiE,WAAW,CAAC,CAAC,KAAI;oBAAG;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACpFvD,OAAA;sBAAK0C,SAAS,EAAC;oBAAmI;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtJ,CACN,eACDvD,OAAA;oBAAK0C,SAAS,EAAC;kBAAkK;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxLvD,OAAA;oBAAK0C,SAAS,EAAC;kBAAuK;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1L;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGhBvD,OAAA;QAAM0C,SAAS,EAAC,8EAA8E;QAAAtC,QAAA,eAC5FJ,OAAA,CAACb,MAAM,CAACqE,GAAG;UACTZ,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9Ba,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1ClB,SAAS,EAAC,QAAQ;UAAAtC,QAAA,EAEjBA;QAAQ;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAGPvD,OAAA,CAACb,MAAM,CAACsF,GAAG;QACT7B,OAAO,EAAE;UAAEC,CAAC,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAE,CAAE;QAChCC,OAAO,EAAE;UAAEF,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAC9BY,UAAU,EAAE;UAAEE,KAAK,EAAE;QAAI,CAAE;QAC3BlB,SAAS,EAAC,8KAA8K;QAAAtC,QAAA,eAExLJ,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAtC,QAAA,eACxBJ,OAAA;YAAK0C,SAAS,EAAC,qBAAqB;YAAAtC,QAAA,eAClCJ,OAAA;cACEgD,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;cACrCT,SAAS,EAAC,0XAA0X;cAAAtC,QAAA,gBAEpYJ,OAAA;gBAAG0C,SAAS,EAAC;cAAyF;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3GvD,OAAA;gBAAM0C,SAAS,EAAC,eAAe;gBAAAtC,QAAA,EAAC;cAAI;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3CvD,OAAA;gBAAK0C,SAAS,EAAC;cAA8I;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9J;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClD,EAAA,CApOQF,cAAc;EAAA,QACJb,WAAW,EAGCA,WAAW,EACFA,WAAW,EAChCD,WAAW,EACXG,WAAW,EACXC,WAAW;AAAA;AAAAiF,EAAA,GARrBvE,cAAc;AAsOvB,eAAeA,cAAc;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}