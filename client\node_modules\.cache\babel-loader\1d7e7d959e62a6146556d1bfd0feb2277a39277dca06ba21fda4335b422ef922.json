{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { chatWithChatGPTToGetAns } from '../../../apicalls/chat';\nimport QuizRenderer from '../../../components/QuizRenderer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const getExamData = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({\n        examId: id\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        var _response$data, _response$data2, _response$data2$quest, _response$data3, _response$data4, _response$data5, _response$data5$quest, _response$data5$quest2, _response$data6, _response$data7;\n        console.log('🎯 QuizPlay Debug - Exam data received:', {\n          examName: (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.name,\n          questionsCount: ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$quest = _response$data2.questions) === null || _response$data2$quest === void 0 ? void 0 : _response$data2$quest.length) || 0,\n          duration: (_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.duration,\n          hasQuestions: !!((_response$data4 = response.data) !== null && _response$data4 !== void 0 && _response$data4.questions),\n          firstQuestion: ((_response$data5 = response.data) === null || _response$data5 === void 0 ? void 0 : (_response$data5$quest = _response$data5.questions) === null || _response$data5$quest === void 0 ? void 0 : (_response$data5$quest2 = _response$data5$quest[0]) === null || _response$data5$quest2 === void 0 ? void 0 : _response$data5$quest2.name) || 'No questions'\n        });\n        setQuestions(((_response$data6 = response.data) === null || _response$data6 === void 0 ? void 0 : _response$data6.questions) || []);\n        setExamData(response.data);\n        setSecondsLeft(((_response$data7 = response.data) === null || _response$data7 === void 0 ? void 0 : _response$data7.duration) || 0);\n      } else {\n        message.error(response.message);\n        navigate('/user/quiz');\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n      navigate('/user/quiz');\n    }\n  }, [id, dispatch, navigate]);\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      questions.forEach((q, idx) => {\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        } else if (q.type === \"mcq\" || q.answerType === \"Options\") {\n          const correctKey = q.correctOption || q.correctAnswer;\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        }\n      });\n      const verdict = correctAnswers.length >= examData.passingMarks ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers,\n        wrongAnswers,\n        verdict\n      };\n      const response = await addReport({\n        exam: id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        navigate(`/quiz/${id}/result`, {\n          state: {\n            result: tempResult\n          }\n        });\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);\n  const startTimer = useCallback(() => {\n    const totalSeconds = (examData === null || examData === void 0 ? void 0 : examData.duration) || 0;\n    setSecondsLeft(totalSeconds);\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  }, [examData]);\n  useEffect(() => {\n    if (timeUp) {\n      clearInterval(intervalId);\n      calculateResult();\n    }\n  }, [timeUp, intervalId, calculateResult]);\n  useEffect(() => {\n    if (id) {\n      getExamData();\n    }\n  }, [id]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Add quiz-fullscreen class for fullscreen experience\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  useEffect(() => {\n    if (examData && questions.length > 0) {\n      startTimer();\n    }\n  }, [examData, questions, startTimer]);\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [intervalId]);\n  if (!examData || questions.length === 0) {\n    var _examData$questions, _examData$questions2, _examData$questions3, _examData$questions$, _examData$questions$$;\n    console.log('🔄 QuizPlay Debug - Loading state:', {\n      hasExamData: !!examData,\n      questionsLength: questions.length,\n      examDataName: examData === null || examData === void 0 ? void 0 : examData.name,\n      examDataQuestions: examData === null || examData === void 0 ? void 0 : (_examData$questions = examData.questions) === null || _examData$questions === void 0 ? void 0 : _examData$questions.length\n    });\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center max-w-md mx-auto p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading quiz questions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 p-4 bg-white rounded-lg shadow text-left text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-semibold text-blue-800 mb-2\",\n            children: \"\\uD83D\\uDD0D Debug Info:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Has Exam Data:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 18\n            }, this), \" \", examData ? 'Yes' : 'No']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Exam Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 18\n            }, this), \" \", (examData === null || examData === void 0 ? void 0 : examData.name) || 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Questions in State:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 18\n            }, this), \" \", questions.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Questions in ExamData:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 18\n            }, this), \" \", (examData === null || examData === void 0 ? void 0 : (_examData$questions2 = examData.questions) === null || _examData$questions2 === void 0 ? void 0 : _examData$questions2.length) || 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Exam Duration:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 18\n            }, this), \" \", (examData === null || examData === void 0 ? void 0 : examData.duration) || 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), (examData === null || examData === void 0 ? void 0 : (_examData$questions3 = examData.questions) === null || _examData$questions3 === void 0 ? void 0 : _examData$questions3.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"First Question:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 20\n            }, this), \" \", ((_examData$questions$ = examData.questions[0]) === null || _examData$questions$ === void 0 ? void 0 : (_examData$questions$$ = _examData$questions$.name) === null || _examData$questions$$ === void 0 ? void 0 : _examData$questions$$.substring(0, 50)) || 'N/A', \"...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(QuizRenderer, {\n    question: questions[selectedQuestionIndex],\n    questionIndex: selectedQuestionIndex,\n    totalQuestions: questions.length,\n    selectedAnswer: selectedOptions[selectedQuestionIndex],\n    onAnswerChange: answer => setSelectedOptions({\n      ...selectedOptions,\n      [selectedQuestionIndex]: answer\n    }),\n    timeLeft: secondsLeft,\n    username: (user === null || user === void 0 ? void 0 : user.name) || \"Student\",\n    onNext: () => {\n      if (selectedQuestionIndex === questions.length - 1) {\n        calculateResult();\n      } else {\n        setSelectedQuestionIndex(selectedQuestionIndex + 1);\n      }\n    },\n    onPrevious: () => setSelectedQuestionIndex(selectedQuestionIndex - 1)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"rxRL7ILUtk3PGnm5rA68VSIZIjY=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "useDispatch", "useSelector", "message", "getExamById", "addReport", "HideLoading", "ShowLoading", "chatWithChatGPTToGetAns", "Quiz<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "QuizPlay", "_s", "examData", "setExamData", "questions", "setQuestions", "selectedQuestionIndex", "setSelectedQuestionIndex", "selectedOptions", "setSelectedOptions", "secondsLeft", "setSecondsLeft", "timeUp", "setTimeUp", "intervalId", "setIntervalId", "id", "navigate", "dispatch", "user", "state", "getExamData", "response", "examId", "success", "_response$data", "_response$data2", "_response$data2$quest", "_response$data3", "_response$data4", "_response$data5", "_response$data5$quest", "_response$data5$quest2", "_response$data6", "_response$data7", "console", "log", "examName", "data", "name", "questionsCount", "length", "duration", "hasQuestions", "firstQuestion", "error", "checkFreeTextAnswers", "payload", "calculateResult", "_id", "freeTextPayload", "for<PERSON>ach", "q", "idx", "type", "answerType", "push", "question", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "userAnswer", "gptResults", "gptMap", "r", "result", "isCorrect", "reason", "correctAnswers", "wrongAnswers", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "verdict", "passingMarks", "tempResult", "exam", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "document", "body", "classList", "add", "remove", "_examData$questions", "_examData$questions2", "_examData$questions3", "_examData$questions$", "_examData$questions$$", "hasExamData", "questions<PERSON><PERSON>th", "examDataName", "examDataQuestions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "substring", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "answer", "timeLeft", "username", "onNext", "onPrevious", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { chatWithChatGPTToGetAns } from '../../../apicalls/chat';\nimport QuizRenderer from '../../../components/QuizRenderer';\n\nconst QuizPlay = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  \n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  const getExamData = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({ examId: id });\n      dispatch(HideLoading());\n      \n      if (response.success) {\n        console.log('🎯 QuizPlay Debug - Exam data received:', {\n          examName: response.data?.name,\n          questionsCount: response.data?.questions?.length || 0,\n          duration: response.data?.duration,\n          hasQuestions: !!response.data?.questions,\n          firstQuestion: response.data?.questions?.[0]?.name || 'No questions'\n        });\n\n        setQuestions(response.data?.questions || []);\n        setExamData(response.data);\n        setSecondsLeft(response.data?.duration || 0);\n      } else {\n        message.error(response.message);\n        navigate('/user/quiz');\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n      navigate('/user/quiz');\n    }\n  }, [id, dispatch, navigate]);\n\n  const checkFreeTextAnswers = async (payload) => {\n    if (!payload.length) return [];\n    const { data } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n\n  const calculateResult = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n\n      dispatch(ShowLoading());\n\n      const freeTextPayload = [];\n      questions.forEach((q, idx) => {\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\",\n          });\n        }\n      });\n\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n\n      gptResults.forEach((r) => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\n        }\n      });\n\n      const correctAnswers = [];\n      const wrongAnswers = [];\n\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        } else if (q.type === \"mcq\" || q.answerType === \"Options\") {\n          const correctKey = q.correctOption || q.correctAnswer;\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = { ...q, userAnswer: userAnswerKey };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        }\n      });\n\n      const verdict = correctAnswers.length >= examData.passingMarks ? \"Pass\" : \"Fail\";\n      const tempResult = { correctAnswers, wrongAnswers, verdict };\n\n      const response = await addReport({\n        exam: id,\n        result: tempResult,\n        user: user._id,\n      });\n\n      if (response.success) {\n        navigate(`/quiz/${id}/result`, { state: { result: tempResult } });\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);\n\n  const startTimer = useCallback(() => {\n    const totalSeconds = examData?.duration || 0;\n    setSecondsLeft(totalSeconds);\n\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft((prevSeconds) => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  }, [examData]);\n\n  useEffect(() => {\n    if (timeUp) {\n      clearInterval(intervalId);\n      calculateResult();\n    }\n  }, [timeUp, intervalId, calculateResult]);\n\n  useEffect(() => {\n    if (id) {\n      getExamData();\n    }\n  }, [id]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Add quiz-fullscreen class for fullscreen experience\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  useEffect(() => {\n    if (examData && questions.length > 0) {\n      startTimer();\n    }\n  }, [examData, questions, startTimer]);\n\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [intervalId]);\n\n  if (!examData || questions.length === 0) {\n    console.log('🔄 QuizPlay Debug - Loading state:', {\n      hasExamData: !!examData,\n      questionsLength: questions.length,\n      examDataName: examData?.name,\n      examDataQuestions: examData?.questions?.length\n    });\n\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center max-w-md mx-auto p-6\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading quiz questions...</p>\n          <div className=\"mt-4 p-4 bg-white rounded-lg shadow text-left text-sm\">\n            <div className=\"font-semibold text-blue-800 mb-2\">🔍 Debug Info:</div>\n            <div><strong>Has Exam Data:</strong> {examData ? 'Yes' : 'No'}</div>\n            <div><strong>Exam Name:</strong> {examData?.name || 'N/A'}</div>\n            <div><strong>Questions in State:</strong> {questions.length}</div>\n            <div><strong>Questions in ExamData:</strong> {examData?.questions?.length || 'N/A'}</div>\n            <div><strong>Exam Duration:</strong> {examData?.duration || 'N/A'}</div>\n            {examData?.questions?.length > 0 && (\n              <div><strong>First Question:</strong> {examData.questions[0]?.name?.substring(0, 50) || 'N/A'}...</div>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <QuizRenderer\n      question={questions[selectedQuestionIndex]}\n      questionIndex={selectedQuestionIndex}\n      totalQuestions={questions.length}\n      selectedAnswer={selectedOptions[selectedQuestionIndex]}\n      onAnswerChange={(answer) =>\n        setSelectedOptions({\n          ...selectedOptions,\n          [selectedQuestionIndex]: answer,\n        })\n      }\n      timeLeft={secondsLeft}\n      username={user?.name || \"Student\"}\n      onNext={() => {\n        if (selectedQuestionIndex === questions.length - 1) {\n          calculateResult();\n        } else {\n          setSelectedQuestionIndex(selectedQuestionIndex + 1);\n        }\n      }}\n      onPrevious={() => setSelectedQuestionIndex(selectedQuestionIndex - 1)}\n    />\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,OAAOC,YAAY,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAElD,MAAM;IAAEgC;EAAG,CAAC,GAAG7B,SAAS,CAAC,CAAC;EAC1B,MAAM8B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8B;EAAK,CAAC,GAAG7B,WAAW,CAAE8B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAME,WAAW,GAAGnC,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFgC,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM2B,QAAQ,GAAG,MAAM9B,WAAW,CAAC;QAAE+B,MAAM,EAAEP;MAAG,CAAC,CAAC;MAClDE,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAI4B,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,eAAA,EAAAC,eAAA;QACpBC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;UACrDC,QAAQ,GAAAZ,cAAA,GAAEH,QAAQ,CAACgB,IAAI,cAAAb,cAAA,uBAAbA,cAAA,CAAec,IAAI;UAC7BC,cAAc,EAAE,EAAAd,eAAA,GAAAJ,QAAQ,CAACgB,IAAI,cAAAZ,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAetB,SAAS,cAAAuB,qBAAA,uBAAxBA,qBAAA,CAA0Bc,MAAM,KAAI,CAAC;UACrDC,QAAQ,GAAAd,eAAA,GAAEN,QAAQ,CAACgB,IAAI,cAAAV,eAAA,uBAAbA,eAAA,CAAec,QAAQ;UACjCC,YAAY,EAAE,CAAC,GAAAd,eAAA,GAACP,QAAQ,CAACgB,IAAI,cAAAT,eAAA,eAAbA,eAAA,CAAezB,SAAS;UACxCwC,aAAa,EAAE,EAAAd,eAAA,GAAAR,QAAQ,CAACgB,IAAI,cAAAR,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAe1B,SAAS,cAAA2B,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA2B,CAAC,CAAC,cAAAC,sBAAA,uBAA7BA,sBAAA,CAA+BO,IAAI,KAAI;QACxD,CAAC,CAAC;QAEFlC,YAAY,CAAC,EAAA4B,eAAA,GAAAX,QAAQ,CAACgB,IAAI,cAAAL,eAAA,uBAAbA,eAAA,CAAe7B,SAAS,KAAI,EAAE,CAAC;QAC5CD,WAAW,CAACmB,QAAQ,CAACgB,IAAI,CAAC;QAC1B3B,cAAc,CAAC,EAAAuB,eAAA,GAAAZ,QAAQ,CAACgB,IAAI,cAAAJ,eAAA,uBAAbA,eAAA,CAAeQ,QAAQ,KAAI,CAAC,CAAC;MAC9C,CAAC,MAAM;QACLnD,OAAO,CAACsD,KAAK,CAACvB,QAAQ,CAAC/B,OAAO,CAAC;QAC/B0B,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACd3B,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MACvBH,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;MAC5B0B,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACD,EAAE,EAAEE,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE5B,MAAM6B,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAACN,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAEH;IAAK,CAAC,GAAG,MAAM1C,uBAAuB,CAACmD,OAAO,CAAC;IACvD,OAAOT,IAAI;EACb,CAAC;EAED,MAAMU,eAAe,GAAG9D,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF,IAAI,CAACiC,IAAI,IAAI,CAACA,IAAI,CAAC8B,GAAG,EAAE;QACtB1D,OAAO,CAACsD,KAAK,CAAC,sCAAsC,CAAC;QACrD5B,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAC,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMuD,eAAe,GAAG,EAAE;MAC1B9C,SAAS,CAAC+C,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,IAAID,CAAC,CAACE,IAAI,KAAK,MAAM,IAAIF,CAAC,CAACG,UAAU,KAAK,WAAW,IAAIH,CAAC,CAACG,UAAU,KAAK,mBAAmB,EAAE;UAC7FL,eAAe,CAACM,IAAI,CAAC;YACnBC,QAAQ,EAAEL,CAAC,CAACb,IAAI;YAChBmB,cAAc,EAAEN,CAAC,CAACO,aAAa,IAAIP,CAAC,CAACQ,aAAa;YAClDC,UAAU,EAAErD,eAAe,CAAC6C,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMS,UAAU,GAAG,MAAMhB,oBAAoB,CAACI,eAAe,CAAC;MAC9D,MAAMa,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACX,OAAO,CAAEa,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACC,MAAM,IAAI,OAAOD,CAAC,CAACC,MAAM,CAACC,SAAS,KAAK,SAAS,EAAE;UACvDH,MAAM,CAACC,CAAC,CAACP,QAAQ,CAAC,GAAGO,CAAC,CAACC,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAOD,CAAC,CAACE,SAAS,KAAK,SAAS,EAAE;UAC3CH,MAAM,CAACC,CAAC,CAACP,QAAQ,CAAC,GAAG;YAAES,SAAS,EAAEF,CAAC,CAACE,SAAS;YAAEC,MAAM,EAAEH,CAAC,CAACG,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MAEvBjE,SAAS,CAAC+C,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,MAAMiB,aAAa,GAAG9D,eAAe,CAAC6C,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAID,CAAC,CAACE,IAAI,KAAK,MAAM,IAAIF,CAAC,CAACG,UAAU,KAAK,WAAW,IAAIH,CAAC,CAACG,UAAU,KAAK,mBAAmB,EAAE;UAC7F,MAAM;YAAEW,SAAS,GAAG,KAAK;YAAEC,MAAM,GAAG;UAAG,CAAC,GAAGJ,MAAM,CAACX,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAMgC,QAAQ,GAAG;YAAE,GAAGnB,CAAC;YAAES,UAAU,EAAES,aAAa;YAAEH;UAAO,CAAC;UAE5D,IAAID,SAAS,EAAE;YACbE,cAAc,CAACZ,IAAI,CAACe,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLF,YAAY,CAACb,IAAI,CAACe,QAAQ,CAAC;UAC7B;QACF,CAAC,MAAM,IAAInB,CAAC,CAACE,IAAI,KAAK,KAAK,IAAIF,CAAC,CAACG,UAAU,KAAK,SAAS,EAAE;UACzD,MAAMiB,UAAU,GAAGpB,CAAC,CAACQ,aAAa,IAAIR,CAAC,CAACO,aAAa;UACrD,MAAMO,SAAS,GAAGM,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGnB,CAAC;YAAES,UAAU,EAAES;UAAc,CAAC;UAEpD,IAAIJ,SAAS,EAAE;YACbE,cAAc,CAACZ,IAAI,CAACe,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLF,YAAY,CAACb,IAAI,CAACe,QAAQ,CAAC;UAC7B;QACF;MACF,CAAC,CAAC;MAEF,MAAME,OAAO,GAAGL,cAAc,CAAC3B,MAAM,IAAIvC,QAAQ,CAACwE,YAAY,GAAG,MAAM,GAAG,MAAM;MAChF,MAAMC,UAAU,GAAG;QAAEP,cAAc;QAAEC,YAAY;QAAEI;MAAQ,CAAC;MAE5D,MAAMnD,QAAQ,GAAG,MAAM7B,SAAS,CAAC;QAC/BmF,IAAI,EAAE5D,EAAE;QACRiD,MAAM,EAAEU,UAAU;QAClBxD,IAAI,EAAEA,IAAI,CAAC8B;MACb,CAAC,CAAC;MAEF,IAAI3B,QAAQ,CAACE,OAAO,EAAE;QACpBP,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;UAAEI,KAAK,EAAE;YAAE6C,MAAM,EAAEU;UAAW;QAAE,CAAC,CAAC;MACnE,CAAC,MAAM;QACLpF,OAAO,CAACsD,KAAK,CAACvB,QAAQ,CAAC/B,OAAO,CAAC;MACjC;MACA2B,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAOmD,KAAK,EAAE;MACd3B,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MACvBH,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACa,SAAS,EAAEI,eAAe,EAAEN,QAAQ,EAAEc,EAAE,EAAEG,IAAI,EAAEF,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAExE,MAAM2D,UAAU,GAAG3F,WAAW,CAAC,MAAM;IACnC,MAAM4F,YAAY,GAAG,CAAA5E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwC,QAAQ,KAAI,CAAC;IAC5C/B,cAAc,CAACmE,YAAY,CAAC;IAE5B,MAAMC,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtCrE,cAAc,CAAEsE,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACLpE,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACRE,aAAa,CAACgE,aAAa,CAAC;EAC9B,CAAC,EAAE,CAAC7E,QAAQ,CAAC,CAAC;EAEdjB,SAAS,CAAC,MAAM;IACd,IAAI2B,MAAM,EAAE;MACVsE,aAAa,CAACpE,UAAU,CAAC;MACzBkC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACpC,MAAM,EAAEE,UAAU,EAAEkC,eAAe,CAAC,CAAC;EAEzC/D,SAAS,CAAC,MAAM;IACd,IAAI+B,EAAE,EAAE;MACNK,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACL,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEV;EACA/B,SAAS,CAAC,MAAM;IACdkG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAE9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENtG,SAAS,CAAC,MAAM;IACd,IAAIiB,QAAQ,IAAIE,SAAS,CAACqC,MAAM,GAAG,CAAC,EAAE;MACpCoC,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC3E,QAAQ,EAAEE,SAAS,EAAEyE,UAAU,CAAC,CAAC;EAErC5F,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAI6B,UAAU,EAAE;QACdoE,aAAa,CAACpE,UAAU,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,IAAI,CAACZ,QAAQ,IAAIE,SAAS,CAACqC,MAAM,KAAK,CAAC,EAAE;IAAA,IAAA+C,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA;IACvCzD,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;MAChDyD,WAAW,EAAE,CAAC,CAAC3F,QAAQ;MACvB4F,eAAe,EAAE1F,SAAS,CAACqC,MAAM;MACjCsD,YAAY,EAAE7F,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqC,IAAI;MAC5ByD,iBAAiB,EAAE9F,QAAQ,aAARA,QAAQ,wBAAAsF,mBAAA,GAARtF,QAAQ,CAAEE,SAAS,cAAAoF,mBAAA,uBAAnBA,mBAAA,CAAqB/C;IAC1C,CAAC,CAAC;IAEF,oBACE1C,OAAA;MAAKkG,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGnG,OAAA;QAAKkG,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CnG,OAAA;UAAKkG,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9FvG,OAAA;UAAGkG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/DvG,OAAA;UAAKkG,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBACpEnG,OAAA;YAAKkG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtEvG,OAAA;YAAAmG,QAAA,gBAAKnG,OAAA;cAAAmG,QAAA,EAAQ;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACpG,QAAQ,GAAG,KAAK,GAAG,IAAI;UAAA;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpEvG,OAAA;YAAAmG,QAAA,gBAAKnG,OAAA;cAAAmG,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,CAAApG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqC,IAAI,KAAI,KAAK;UAAA;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChEvG,OAAA;YAAAmG,QAAA,gBAAKnG,OAAA;cAAAmG,QAAA,EAAQ;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClG,SAAS,CAACqC,MAAM;UAAA;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClEvG,OAAA;YAAAmG,QAAA,gBAAKnG,OAAA;cAAAmG,QAAA,EAAQ;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,CAAApG,QAAQ,aAARA,QAAQ,wBAAAuF,oBAAA,GAARvF,QAAQ,CAAEE,SAAS,cAAAqF,oBAAA,uBAAnBA,oBAAA,CAAqBhD,MAAM,KAAI,KAAK;UAAA;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzFvG,OAAA;YAAAmG,QAAA,gBAAKnG,OAAA;cAAAmG,QAAA,EAAQ;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,CAAApG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwC,QAAQ,KAAI,KAAK;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACvE,CAAApG,QAAQ,aAARA,QAAQ,wBAAAwF,oBAAA,GAARxF,QAAQ,CAAEE,SAAS,cAAAsF,oBAAA,uBAAnBA,oBAAA,CAAqBjD,MAAM,IAAG,CAAC,iBAC9B1C,OAAA;YAAAmG,QAAA,gBAAKnG,OAAA;cAAAmG,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,EAAAX,oBAAA,GAAAzF,QAAQ,CAACE,SAAS,CAAC,CAAC,CAAC,cAAAuF,oBAAA,wBAAAC,qBAAA,GAArBD,oBAAA,CAAuBpD,IAAI,cAAAqD,qBAAA,uBAA3BA,qBAAA,CAA6BW,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAI,KAAK,EAAC,KAAG;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACvG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvG,OAAA,CAACF,YAAY;IACX4D,QAAQ,EAAErD,SAAS,CAACE,qBAAqB,CAAE;IAC3CkG,aAAa,EAAElG,qBAAsB;IACrCmG,cAAc,EAAErG,SAAS,CAACqC,MAAO;IACjCiE,cAAc,EAAElG,eAAe,CAACF,qBAAqB,CAAE;IACvDqG,cAAc,EAAGC,MAAM,IACrBnG,kBAAkB,CAAC;MACjB,GAAGD,eAAe;MAClB,CAACF,qBAAqB,GAAGsG;IAC3B,CAAC,CACF;IACDC,QAAQ,EAAEnG,WAAY;IACtBoG,QAAQ,EAAE,CAAA3F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,KAAI,SAAU;IAClCwE,MAAM,EAAEA,CAAA,KAAM;MACZ,IAAIzG,qBAAqB,KAAKF,SAAS,CAACqC,MAAM,GAAG,CAAC,EAAE;QAClDO,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM;QACLzC,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;MACrD;IACF,CAAE;IACF0G,UAAU,EAAEA,CAAA,KAAMzG,wBAAwB,CAACD,qBAAqB,GAAG,CAAC;EAAE;IAAA6F,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvE,CAAC;AAEN,CAAC;AAACrG,EAAA,CA7OID,QAAQ;EAAA,QASGb,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAA2H,EAAA,GAZxBjH,QAAQ;AA+Od,eAAeA,QAAQ;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}