{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport \"./index.css\";\nimport { Link } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowBigRightLinesFilled, TbBrain, TbBook, TbTrophy, TbUsers, TbStar, TbSchool, TbMenu2, TbX, TbMoon, TbSun } from \"react-icons/tb\";\nimport { Rate, message } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReviews } from \"../../../apicalls/reviews\";\nimport Image1 from \"../../../assets/collage-1.png\";\nimport Image2 from \"../../../assets/collage-2.png\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport { useTheme } from \"../../../contexts/ThemeContext\";\nimport { Button } from \"../../../components/modern\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const homeSectionRef = useRef(null);\n  const reviewsSectionRef = useRef(null);\n  const contactUsRef = useRef(null);\n  const [reviews, setReviews] = useState([]);\n  const dispatch = useDispatch();\n  const [menuOpen, setMenuOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [responseMessage, setResponseMessage] = useState(\"\");\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  useEffect(() => {\n    getReviews();\n  }, []);\n  const getReviews = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getAllReviews();\n      if (response.success) {\n        setReviews(response.data);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    dispatch(HideLoading());\n  };\n  const scrollToSection = (ref, offset = 80) => {\n    if (ref !== null && ref !== void 0 && ref.current) {\n      const sectionTop = ref.current.offsetTop;\n      window.scrollTo({\n        top: sectionTop - offset,\n        behavior: \"smooth\"\n      });\n      // Close mobile menu after clicking\n      setMenuOpen(false);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setResponseMessage(\"\");\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setResponseMessage(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        setResponseMessage(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      setResponseMessage(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Home\",\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-header fixed w-full top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-16\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"logo-text\",\n              children: [\"Brain\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"logo-accent\",\n                children: \"Wave\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 22\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(homeSectionRef),\n              className: \"nav-item\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(reviewsSectionRef),\n              className: \"nav-item\",\n              children: \"Reviews\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactUsRef),\n              className: \"nav-item\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: toggleTheme,\n              className: \"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\",\n              children: isDarkMode ? /*#__PURE__*/_jsxDEV(TbSun, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 31\n              }, this) : /*#__PURE__*/_jsxDEV(TbMoon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 63\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setMenuOpen(!menuOpen),\n              className: \"md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\",\n              children: menuOpen ? /*#__PURE__*/_jsxDEV(TbX, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 29\n              }, this) : /*#__PURE__*/_jsxDEV(TbMenu2, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 59\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), menuOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          className: \"md:hidden mobile-nav\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(homeSectionRef),\n              className: \"nav-item\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(reviewsSectionRef),\n              className: \"nav-item\",\n              children: \"Reviews\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactUsRef),\n              className: \"nav-item\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: homeSectionRef,\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-grid\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"hero-content\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"hero-badge\",\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-5 h-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), \"#1 Educational Platform in Tanzania\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"hero-title\",\n              children: [\"Fueling Bright Futures with\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gradient\",\n                children: [\"Education\", /*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {\n                  className: \"inline w-8 h-8 ml-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"hero-subtitle\",\n              children: \"Discover limitless learning opportunities with our comprehensive online study platform. Study anywhere, anytime, and achieve your academic goals with confidence.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"cta-section\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-primary btn-large\",\n                  children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                    className: \"w-5 h-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 21\n                  }, this), \"Explore Platform\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.6\n              },\n              className: \"trust-indicators\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"15K+ Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  style: {\n                    color: '#f59e0b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"4.9/5 Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Award Winning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.3\n            },\n            className: \"hero-image\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: Image1,\n                alt: \"Students Learning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-10, 10, -10]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity\n                },\n                className: \"floating-element\",\n                style: {\n                  top: '-1rem',\n                  left: '-1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(TbBook, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [10, -10, 10]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                className: \"floating-element\",\n                style: {\n                  bottom: '-1rem',\n                  right: '-1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  style: {\n                    color: '#f59e0b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"stats-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"stats-grid\",\n          children: [{\n            number: \"15K+\",\n            text: \"Active Students\"\n          }, {\n            number: \"500+\",\n            text: \"Expert Teachers\"\n          }, {\n            number: \"1000+\",\n            text: \"Video Lessons\"\n          }, {\n            number: \"98%\",\n            text: \"Success Rate\"\n          }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: stat.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"stat-text\",\n              children: stat.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: reviewsSectionRef,\n      className: \"reviews-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reviews-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"reviews-title\",\n          children: \"Reviews from our students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reviews-grid\",\n          children: [{\n            rating: 5,\n            text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\n            user: {\n              name: \"Sarah Johnson\"\n            }\n          }, {\n            rating: 5,\n            text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\n            user: {\n              name: \"Michael Chen\"\n            }\n          }, {\n            rating: 5,\n            text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\n            user: {\n              name: \"Amina Hassan\"\n            }\n          }].map((review, index) => {\n            var _review$user;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              className: \"review-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-rating\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#f59e0b',\n                    fontSize: '1.25rem'\n                  },\n                  children: '★'.repeat(review.rating)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-text\",\n                children: [\"\\\"\", review.text, \"\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-divider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-author\",\n                children: (_review$user = review.user) === null || _review$user === void 0 ? void 0 : _review$user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactUsRef,\n      className: \"contact-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"contact-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"contact-title\",\n          children: \"Contact Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"contact-subtitle\",\n          children: \"Get in touch with us for any questions or support\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"contact-form\",\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"name\",\n              placeholder: \"Your Name\",\n              className: \"form-input\",\n              value: formData.name,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              name: \"email\",\n              placeholder: \"Your Email\",\n              className: \"form-input\",\n              value: formData.email,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"message\",\n              placeholder: \"Your Message\",\n              className: \"form-input form-textarea\",\n              value: formData.message,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"form-submit\",\n            disabled: loading,\n            children: loading ? \"Sending...\" : \"Send Message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this), responseMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"response-message\",\n            style: {\n              marginTop: '1rem',\n              textAlign: 'center',\n              color: '#10b981'\n            },\n            children: responseMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"footer-text\",\n          children: \"\\xA9 2024 BrainWave Educational Platform. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"MRDF8V1ATp3lGHlUHsxbY47h2p0=\", false, function () {\n  return [useDispatch, useTheme];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Link", "motion", "TbArrowBigRightLinesFilled", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbStar", "TbSchool", "TbMenu2", "TbX", "TbMoon", "TbSun", "Rate", "message", "useDispatch", "HideLoading", "ShowLoading", "getAllReviews", "Image1", "Image2", "contactUs", "useTheme", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Home", "_s", "homeSectionRef", "reviewsSectionRef", "contactUsRef", "reviews", "setReviews", "dispatch", "menuOpen", "setMenuOpen", "formData", "setFormData", "name", "email", "loading", "setLoading", "responseMessage", "setResponseMessage", "isDarkMode", "toggleTheme", "getReviews", "response", "success", "data", "error", "scrollToSection", "ref", "offset", "current", "sectionTop", "offsetTop", "window", "scrollTo", "top", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "className", "children", "nav", "initial", "y", "opacity", "animate", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "div", "exit", "x", "transition", "duration", "delay", "style", "color", "src", "alt", "repeat", "Infinity", "left", "bottom", "right", "whileInView", "viewport", "once", "number", "text", "map", "stat", "index", "rating", "user", "review", "_review$user", "fontSize", "onSubmit", "type", "placeholder", "onChange", "required", "disabled", "marginTop", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowBigRightLinesFilled,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbStar,\r\n  TbSchool,\r\n  TbMenu2,\r\n  TbX,\r\n  TbMoon,\r\n  TbSun\r\n} from \"react-icons/tb\";\r\nimport { Rate, message } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReviews } from \"../../../apicalls/reviews\";\r\nimport Image1 from \"../../../assets/collage-1.png\";\r\nimport Image2 from \"../../../assets/collage-2.png\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport { useTheme } from \"../../../contexts/ThemeContext\";\r\nimport { Button } from \"../../../components/modern\";\r\n\r\nconst Home = () => {\r\n  const homeSectionRef = useRef(null);\r\n  const reviewsSectionRef = useRef(null);\r\n  const contactUsRef = useRef(null);\r\n  const [reviews, setReviews] = useState([]);\r\n  const dispatch = useDispatch();\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const [responseMessage, setResponseMessage] = useState(\"\");\r\n  const { isDarkMode, toggleTheme } = useTheme();\r\n\r\n  useEffect(() => { getReviews(); }, []);\r\n\r\n  const getReviews = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getAllReviews();\r\n      if (response.success) {\r\n        setReviews(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n    dispatch(HideLoading());\r\n  };\r\n\r\n  const scrollToSection = (ref, offset = 80) => {\r\n    if (ref?.current) {\r\n      const sectionTop = ref.current.offsetTop;\r\n      window.scrollTo({ top: sectionTop - offset, behavior: \"smooth\" });\r\n      // Close mobile menu after clicking\r\n      setMenuOpen(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setResponseMessage(\"\");\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setResponseMessage(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        setResponseMessage(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      setResponseMessage(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"Home\">\r\n      {/* Navigation */}\r\n      <motion.nav\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"nav-header fixed w-full top-0 z-50\"\r\n      >\r\n        <div className=\"container\">\r\n          <div className=\"flex items-center justify-between h-16\">\r\n            {/* Logo */}\r\n            <Link to=\"/\" className=\"flex items-center\">\r\n              <span className=\"logo-text\">\r\n                Brain<span className=\"logo-accent\">Wave</span>\r\n              </span>\r\n            </Link>\r\n\r\n            {/* Desktop Navigation */}\r\n            <div className=\"hidden md:flex items-center space-x-8\">\r\n              <button onClick={() => scrollToSection(homeSectionRef)} className=\"nav-item\">Home</button>\r\n              <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item\">Reviews</button>\r\n              <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item\">Contact Us</button>\r\n            </div>\r\n\r\n            {/* Right Section */}\r\n            <div className=\"flex items-center space-x-4\">\r\n              {/* Theme Toggle */}\r\n              <button\r\n                onClick={toggleTheme}\r\n                className=\"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n              >\r\n                {isDarkMode ? <TbSun className=\"w-5 h-5\" /> : <TbMoon className=\"w-5 h-5\" />}\r\n              </button>\r\n\r\n              {/* Mobile Menu Button */}\r\n              <button\r\n                onClick={() => setMenuOpen(!menuOpen)}\r\n                className=\"md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n              >\r\n                {menuOpen ? <TbX className=\"w-6 h-6\" /> : <TbMenu2 className=\"w-6 h-6\" />}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          {menuOpen && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -20 }}\r\n              className=\"md:hidden mobile-nav\"\r\n            >\r\n              <div className=\"flex flex-col\">\r\n                <button onClick={() => scrollToSection(homeSectionRef)} className=\"nav-item\">Home</button>\r\n                <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item\">Reviews</button>\r\n                <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item\">Contact Us</button>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </div>\r\n      </motion.nav>\r\n\r\n      {/* Hero Section */}\r\n      <section ref={homeSectionRef} className=\"hero-section\">\r\n        <div className=\"container\">\r\n          <div className=\"hero-grid\">\r\n            {/* Hero Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"hero-content\"\r\n            >\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"hero-badge\"\r\n              >\r\n                <TbSchool className=\"w-5 h-5 mr-2\" />\r\n                #1 Educational Platform in Tanzania\r\n              </motion.div>\r\n\r\n              <h1 className=\"hero-title\">\r\n                Fueling Bright Futures with{\" \"}\r\n                <span className=\"text-gradient\">\r\n                  Education\r\n                  <TbArrowBigRightLinesFilled className=\"inline w-8 h-8 ml-2\" />\r\n                </span>\r\n              </h1>\r\n\r\n              <p className=\"hero-subtitle\">\r\n                Discover limitless learning opportunities with our comprehensive\r\n                online study platform. Study anywhere, anytime, and achieve your\r\n                academic goals with confidence.\r\n              </p>\r\n\r\n              {/* Explore Platform Button */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                className=\"cta-section\"\r\n              >\r\n                <Link to=\"/login\">\r\n                  <button className=\"btn btn-primary btn-large\">\r\n                    <TbBrain className=\"w-5 h-5 mr-2\" />\r\n                    Explore Platform\r\n                  </button>\r\n                </Link>\r\n              </motion.div>\r\n\r\n              {/* Trust Indicators */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"trust-indicators\"\r\n              >\r\n                <div className=\"trust-indicator\">\r\n                  <TbUsers style={{color: '#007BFF'}} />\r\n                  <span>15K+ Students</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbStar style={{color: '#f59e0b'}} />\r\n                  <span>4.9/5 Rating</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbTrophy style={{color: '#007BFF'}} />\r\n                  <span>Award Winning</span>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              className=\"hero-image\"\r\n            >\r\n              <div className=\"relative\">\r\n                <img\r\n                  src={Image1}\r\n                  alt=\"Students Learning\"\r\n                />\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{top: '-1rem', left: '-1rem'}}\r\n                >\r\n                  <TbBook style={{color: '#007BFF'}} />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{bottom: '-1rem', right: '-1rem'}}\r\n                >\r\n                  <TbTrophy style={{color: '#f59e0b'}} />\r\n                </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Stats Section */}\r\n      <section className=\"stats-section\">\r\n        <div className=\"container\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"stats-grid\"\r\n          >\r\n            {[\r\n              { number: \"15K+\", text: \"Active Students\" },\r\n              { number: \"500+\", text: \"Expert Teachers\" },\r\n              { number: \"1000+\", text: \"Video Lessons\" },\r\n              { number: \"98%\", text: \"Success Rate\" }\r\n            ].map((stat, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"stat-item\"\r\n              >\r\n                <div className=\"stat-number\">{stat.number}</div>\r\n                <p className=\"stat-text\">{stat.text}</p>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Reviews Section */}\r\n      <section ref={reviewsSectionRef} className=\"reviews-section\">\r\n        <div className=\"reviews-container\">\r\n          <h2 className=\"reviews-title\">\r\n            Reviews from our students\r\n          </h2>\r\n          <div className=\"reviews-grid\">\r\n            {[\r\n              {\r\n                rating: 5,\r\n                text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\r\n                user: { name: \"Sarah Johnson\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\r\n                user: { name: \"Michael Chen\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\r\n                user: { name: \"Amina Hassan\" }\r\n              }\r\n            ].map((review, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"review-card\"\r\n              >\r\n                <div className=\"review-rating\">\r\n                  <div style={{ color: '#f59e0b', fontSize: '1.25rem' }}>\r\n                    {'★'.repeat(review.rating)}\r\n                  </div>\r\n                </div>\r\n                <div className=\"review-text\">\"{review.text}\"</div>\r\n                <div className=\"review-divider\"></div>\r\n                <div className=\"review-author\">{review.user?.name}</div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section ref={contactUsRef} className=\"contact-section\">\r\n        <div className=\"contact-container\">\r\n          <h2 className=\"contact-title\">Contact Us</h2>\r\n          <p className=\"contact-subtitle\">Get in touch with us for any questions or support</p>\r\n          <form className=\"contact-form\" onSubmit={handleSubmit}>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Name</label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Your Name\"\r\n                className=\"form-input\"\r\n                value={formData.name}\r\n                onChange={handleChange}\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Email</label>\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Your Email\"\r\n                className=\"form-input\"\r\n                value={formData.email}\r\n                onChange={handleChange}\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Message</label>\r\n              <textarea\r\n                name=\"message\"\r\n                placeholder=\"Your Message\"\r\n                className=\"form-input form-textarea\"\r\n                value={formData.message}\r\n                onChange={handleChange}\r\n                required\r\n              ></textarea>\r\n            </div>\r\n            <button\r\n              type=\"submit\"\r\n              className=\"form-submit\"\r\n              disabled={loading}\r\n            >\r\n              {loading ? \"Sending...\" : \"Send Message\"}\r\n            </button>\r\n            {responseMessage && (\r\n              <p className=\"response-message\" style={{ marginTop: '1rem', textAlign: 'center', color: '#10b981' }}>\r\n                {responseMessage}\r\n              </p>\r\n            )}\r\n          </form>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"footer\">\r\n        <div className=\"footer-content\">\r\n          <p className=\"footer-text\">\r\n            © 2024 BrainWave Educational Platform. All rights reserved.\r\n          </p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,0BAA0B,EAC1BC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,KAAK,QACA,gBAAgB;AACvB,SAASC,IAAI,EAAEC,OAAO,QAAQ,MAAM;AACpC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,MAAM,MAAM,+BAA+B;AAClD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,MAAM,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,cAAc,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM8B,iBAAiB,GAAG9B,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM+B,YAAY,GAAG/B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAMoC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC;IAAEyC,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEzB,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM;IAAE+C,UAAU;IAAEC;EAAY,CAAC,GAAGvB,QAAQ,CAAC,CAAC;EAE9CxB,SAAS,CAAC,MAAM;IAAEgD,UAAU,CAAC,CAAC;EAAE,CAAC,EAAE,EAAE,CAAC;EAEtC,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7Bb,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM8B,QAAQ,GAAG,MAAM7B,aAAa,CAAC,CAAC;MACtC,IAAI6B,QAAQ,CAACC,OAAO,EAAE;QACpBhB,UAAU,CAACe,QAAQ,CAACE,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLnC,OAAO,CAACoC,KAAK,CAACH,QAAQ,CAACjC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdpC,OAAO,CAACoC,KAAK,CAACA,KAAK,CAACpC,OAAO,CAAC;IAC9B;IACAmB,QAAQ,CAACjB,WAAW,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,MAAMmC,eAAe,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAG,EAAE,KAAK;IAC5C,IAAID,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEE,OAAO,EAAE;MAChB,MAAMC,UAAU,GAAGH,GAAG,CAACE,OAAO,CAACE,SAAS;MACxCC,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAEJ,UAAU,GAAGF,MAAM;QAAEO,QAAQ,EAAE;MAAS,CAAC,CAAC;MACjE;MACAzB,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM0B,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAExB,IAAI;MAAEyB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC3B,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGyB;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBzB,UAAU,CAAC,IAAI,CAAC;IAChBE,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAI;MACF,MAAMM,IAAI,GAAG,MAAM5B,SAAS,CAACe,QAAQ,CAAC;MACtC,IAAIa,IAAI,CAACD,OAAO,EAAE;QAChBlC,OAAO,CAACkC,OAAO,CAAC,4BAA4B,CAAC;QAC7CL,kBAAkB,CAAC,4BAA4B,CAAC;QAChDN,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEzB,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACL6B,kBAAkB,CAACM,IAAI,CAACnC,OAAO,IAAI,uBAAuB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdP,kBAAkB,CAAC,0CAA0C,CAAC;IAChE;IACAF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEhB,OAAA;IAAK0C,SAAS,EAAC,MAAM;IAAAC,QAAA,gBAEnB3C,OAAA,CAACxB,MAAM,CAACoE,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eAE9C3C,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3C,OAAA;UAAK0C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD3C,OAAA,CAACzB,IAAI;YAAC0E,EAAE,EAAC,GAAG;YAACP,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eACxC3C,OAAA;cAAM0C,SAAS,EAAC,WAAW;cAAAC,QAAA,GAAC,OACrB,eAAA3C,OAAA;gBAAM0C,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPrD,OAAA;YAAK0C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD3C,OAAA;cAAQsD,OAAO,EAAEA,CAAA,KAAM5B,eAAe,CAACvB,cAAc,CAAE;cAACuC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1FrD,OAAA;cAAQsD,OAAO,EAAEA,CAAA,KAAM5B,eAAe,CAACtB,iBAAiB,CAAE;cAACsC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChGrD,OAAA;cAAQsD,OAAO,EAAEA,CAAA,KAAM5B,eAAe,CAACrB,YAAY,CAAE;cAACqC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAU;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,eAGNrD,OAAA;YAAK0C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAE1C3C,OAAA;cACEsD,OAAO,EAAElC,WAAY;cACrBsB,SAAS,EAAC,2FAA2F;cAAAC,QAAA,EAEpGxB,UAAU,gBAAGnB,OAAA,CAACb,KAAK;gBAACuD,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGrD,OAAA,CAACd,MAAM;gBAACwD,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAGTrD,OAAA;cACEsD,OAAO,EAAEA,CAAA,KAAM5C,WAAW,CAAC,CAACD,QAAQ,CAAE;cACtCiC,SAAS,EAAC,qGAAqG;cAAAC,QAAA,EAE9GlC,QAAQ,gBAAGT,OAAA,CAACf,GAAG;gBAACyD,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGrD,OAAA,CAAChB,OAAO;gBAAC0D,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL5C,QAAQ,iBACPT,OAAA,CAACxB,MAAM,CAAC+E,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BU,IAAI,EAAE;YAAET,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BJ,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eAEhC3C,OAAA;YAAK0C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B3C,OAAA;cAAQsD,OAAO,EAAEA,CAAA,KAAM5B,eAAe,CAACvB,cAAc,CAAE;cAACuC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1FrD,OAAA;cAAQsD,OAAO,EAAEA,CAAA,KAAM5B,eAAe,CAACtB,iBAAiB,CAAE;cAACsC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChGrD,OAAA;cAAQsD,OAAO,EAAEA,CAAA,KAAM5B,eAAe,CAACrB,YAAY,CAAE;cAACqC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAU;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbrD,OAAA;MAAS2B,GAAG,EAAExB,cAAe;MAACuC,SAAS,EAAC,cAAc;MAAAC,QAAA,eACpD3C,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB3C,OAAA,CAACxB,MAAM,CAAC+E,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEU,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCT,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEU,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BjB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAExB3C,OAAA,CAACxB,MAAM,CAAC+E,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BY,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ClB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAEtB3C,OAAA,CAACjB,QAAQ;gBAAC2D,SAAS,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uCAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbrD,OAAA;cAAI0C,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,6BACE,EAAC,GAAG,eAC/B3C,OAAA;gBAAM0C,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,WAE9B,eAAA3C,OAAA,CAACvB,0BAA0B;kBAACiE,SAAS,EAAC;gBAAqB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAELrD,OAAA;cAAG0C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAGJrD,OAAA,CAACxB,MAAM,CAAC+E,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BY,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ClB,SAAS,EAAC,aAAa;cAAAC,QAAA,eAEvB3C,OAAA,CAACzB,IAAI;gBAAC0E,EAAE,EAAC,QAAQ;gBAAAN,QAAA,eACf3C,OAAA;kBAAQ0C,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBAC3C3C,OAAA,CAACtB,OAAO;oBAACgE,SAAS,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,oBAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAGbrD,OAAA,CAACxB,MAAM,CAAC+E,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BY,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ClB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAE5B3C,OAAA;gBAAK0C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B3C,OAAA,CAACnB,OAAO;kBAACgF,KAAK,EAAE;oBAACC,KAAK,EAAE;kBAAS;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtCrD,OAAA;kBAAA2C,QAAA,EAAM;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACNrD,OAAA;gBAAK0C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B3C,OAAA,CAAClB,MAAM;kBAAC+E,KAAK,EAAE;oBAACC,KAAK,EAAE;kBAAS;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCrD,OAAA;kBAAA2C,QAAA,EAAM;gBAAY;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNrD,OAAA;gBAAK0C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B3C,OAAA,CAACpB,QAAQ;kBAACiF,KAAK,EAAE;oBAACC,KAAK,EAAE;kBAAS;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCrD,OAAA;kBAAA2C,QAAA,EAAM;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbrD,OAAA,CAACxB,MAAM,CAAC+E,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEU,CAAC,EAAE;YAAG,CAAE;YAC/BT,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEU,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1ClB,SAAS,EAAC,YAAY;YAAAC,QAAA,eAEtB3C,OAAA;cAAK0C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB3C,OAAA;gBACE+D,GAAG,EAAErE,MAAO;gBACZsE,GAAG,EAAC;cAAmB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eAGFrD,OAAA,CAACxB,MAAM,CAAC+E,GAAG;gBACTP,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;gBAAE,CAAE;gBAC/BY,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEM,MAAM,EAAEC;gBAAS,CAAE;gBAC9CxB,SAAS,EAAC,kBAAkB;gBAC5BmB,KAAK,EAAE;kBAAC3B,GAAG,EAAE,OAAO;kBAAEiC,IAAI,EAAE;gBAAO,CAAE;gBAAAxB,QAAA,eAErC3C,OAAA,CAACrB,MAAM;kBAACkF,KAAK,EAAE;oBAACC,KAAK,EAAE;kBAAS;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEbrD,OAAA,CAACxB,MAAM,CAAC+E,GAAG;gBACTP,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBAAE,CAAE;gBAC9BY,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEM,MAAM,EAAEC;gBAAS,CAAE;gBAC9CxB,SAAS,EAAC,kBAAkB;gBAC5BmB,KAAK,EAAE;kBAACO,MAAM,EAAE,OAAO;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAA1B,QAAA,eAEzC3C,OAAA,CAACpB,QAAQ;kBAACiF,KAAK,EAAE;oBAACC,KAAK,EAAE;kBAAS;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrD,OAAA;MAAS0C,SAAS,EAAC,eAAe;MAAAC,QAAA,eAChC3C,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB3C,OAAA,CAACxB,MAAM,CAAC+E,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BwB,WAAW,EAAE;YAAEvB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCY,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BY,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB9B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAErB,CACC;YAAE8B,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE;UAAkB,CAAC,EAC3C;YAAED,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE;UAAkB,CAAC,EAC3C;YAAED,MAAM,EAAE,OAAO;YAAEC,IAAI,EAAE;UAAgB,CAAC,EAC1C;YAAED,MAAM,EAAE,KAAK;YAAEC,IAAI,EAAE;UAAe,CAAC,CACxC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChB7E,OAAA,CAACxB,MAAM,CAAC+E,GAAG;YAETV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BwB,WAAW,EAAE;cAAEvB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCY,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAEiB,KAAK,GAAG;YAAI,CAAE;YAClDN,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB9B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAErB3C,OAAA;cAAK0C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEiC,IAAI,CAACH;YAAM;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDrD,OAAA;cAAG0C,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEiC,IAAI,CAACF;YAAI;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GARnCwB,KAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrD,OAAA;MAAS2B,GAAG,EAAEvB,iBAAkB;MAACsC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC1D3C,OAAA;QAAK0C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC3C,OAAA;UAAI0C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE9B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrD,OAAA;UAAK0C,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B,CACC;YACEmC,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,yIAAyI;YAC/IK,IAAI,EAAE;cAAElE,IAAI,EAAE;YAAgB;UAChC,CAAC,EACD;YACEiE,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,uIAAuI;YAC7IK,IAAI,EAAE;cAAElE,IAAI,EAAE;YAAe;UAC/B,CAAC,EACD;YACEiE,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,mHAAmH;YACzHK,IAAI,EAAE;cAAElE,IAAI,EAAE;YAAe;UAC/B,CAAC,CACF,CAAC8D,GAAG,CAAC,CAACK,MAAM,EAAEH,KAAK;YAAA,IAAAI,YAAA;YAAA,oBAClBjF,OAAA,CAACxB,MAAM,CAAC+E,GAAG;cAETV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BwB,WAAW,EAAE;gBAAEvB,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAClCY,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEiB,KAAK,GAAG;cAAI,CAAE;cAClDN,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzB9B,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvB3C,OAAA;gBAAK0C,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B3C,OAAA;kBAAK6D,KAAK,EAAE;oBAAEC,KAAK,EAAE,SAAS;oBAAEoB,QAAQ,EAAE;kBAAU,CAAE;kBAAAvC,QAAA,EACnD,GAAG,CAACsB,MAAM,CAACe,MAAM,CAACF,MAAM;gBAAC;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrD,OAAA;gBAAK0C,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAC,IAAC,EAACqC,MAAM,CAACN,IAAI,EAAC,IAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClDrD,OAAA;gBAAK0C,SAAS,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCrD,OAAA;gBAAK0C,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAAsC,YAAA,GAAED,MAAM,CAACD,IAAI,cAAAE,YAAA,uBAAXA,YAAA,CAAapE;cAAI;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAdnDwB,KAAK;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeA,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrD,OAAA;MAAS2B,GAAG,EAAEtB,YAAa;MAACqC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eACrD3C,OAAA;QAAK0C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC3C,OAAA;UAAI0C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CrD,OAAA;UAAG0C,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAiD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrFrD,OAAA;UAAM0C,SAAS,EAAC,cAAc;UAACyC,QAAQ,EAAE3C,YAAa;UAAAG,QAAA,gBACpD3C,OAAA;YAAK0C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3C,OAAA;cAAO0C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CrD,OAAA;cACEoF,IAAI,EAAC,MAAM;cACXvE,IAAI,EAAC,MAAM;cACXwE,WAAW,EAAC,WAAW;cACvB3C,SAAS,EAAC,YAAY;cACtBJ,KAAK,EAAE3B,QAAQ,CAACE,IAAK;cACrByE,QAAQ,EAAElD,YAAa;cACvBmD,QAAQ;YAAA;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNrD,OAAA;YAAK0C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3C,OAAA;cAAO0C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3CrD,OAAA;cACEoF,IAAI,EAAC,OAAO;cACZvE,IAAI,EAAC,OAAO;cACZwE,WAAW,EAAC,YAAY;cACxB3C,SAAS,EAAC,YAAY;cACtBJ,KAAK,EAAE3B,QAAQ,CAACG,KAAM;cACtBwE,QAAQ,EAAElD,YAAa;cACvBmD,QAAQ;YAAA;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNrD,OAAA;YAAK0C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3C,OAAA;cAAO0C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CrD,OAAA;cACEa,IAAI,EAAC,SAAS;cACdwE,WAAW,EAAC,cAAc;cAC1B3C,SAAS,EAAC,0BAA0B;cACpCJ,KAAK,EAAE3B,QAAQ,CAACtB,OAAQ;cACxBiG,QAAQ,EAAElD,YAAa;cACvBmD,QAAQ;YAAA;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrD,OAAA;YACEoF,IAAI,EAAC,QAAQ;YACb1C,SAAS,EAAC,aAAa;YACvB8C,QAAQ,EAAEzE,OAAQ;YAAA4B,QAAA,EAEjB5B,OAAO,GAAG,YAAY,GAAG;UAAc;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,EACRpC,eAAe,iBACdjB,OAAA;YAAG0C,SAAS,EAAC,kBAAkB;YAACmB,KAAK,EAAE;cAAE4B,SAAS,EAAE,MAAM;cAAEC,SAAS,EAAE,QAAQ;cAAE5B,KAAK,EAAE;YAAU,CAAE;YAAAnB,QAAA,EACjG1B;UAAe;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrD,OAAA;MAAQ0C,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxB3C,OAAA;QAAK0C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B3C,OAAA;UAAG0C,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE3B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnD,EAAA,CA1XID,IAAI;EAAA,QAKSX,WAAW,EAKQO,QAAQ;AAAA;AAAA8F,EAAA,GAVxC1F,IAAI;AA4XV,eAAeA,IAAI;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}