{"ast": null, "code": "// Modern UI Components\nexport { default as But<PERSON> } from './Button';\nexport { default as Card } from './Card';\nexport { default as Input } from './Input';\nexport { default as Loading } from './Loading';\n\n// Quiz Components\nexport { default as QuizCard, QuizGrid } from './QuizCard';\nexport { default as QuizQuestion } from './QuizQuestion';\nexport { default as QuizTimer, QuizTimerOverlay } from './QuizTimer';\n\n// Theme & Performance Components\nexport { default as ThemeToggle, ThemeSwitch, ThemeToggleWithLabel } from './ThemeToggle';\nexport { default as LazyImage } from './LazyImage';\nexport { default as ErrorBoundary, ErrorFallback } from './ErrorBoundary';\n\n// Responsive Components\nexport { default as ResponsiveContainer, ResponsiveGrid, ResponsiveText, MobileFirst, DesktopFirst, ResponsiveStack, ResponsiveShow } from './ResponsiveContainer';\n\n// Performance Components (PerformanceIndicator removed)\nexport { usePerformanceMonitor, LazyWrapper, OptimizedImage, useDebouncedSearch, VirtualList } from './PerformanceMonitor';\n\n// Theme Context\nexport { ThemeProvider, useTheme } from '../../contexts/ThemeContext';", "map": {"version": 3, "names": ["default", "<PERSON><PERSON>", "Card", "Input", "Loading", "QuizCard", "QuizGrid", "QuizQuestion", "QuizTimer", "QuizTimer<PERSON><PERSON>lay", "ThemeToggle", "ThemeSwitch", "ThemeToggleWithLabel", "LazyImage", "Error<PERSON>ou<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ResponsiveContainer", "ResponsiveGrid", "ResponsiveText", "MobileFirst", "DesktopFirst", "ResponsiveStack", "ResponsiveShow", "usePerformanceMonitor", "LazyWrapper", "OptimizedImage", "useDebouncedSearch", "VirtualList", "ThemeProvider", "useTheme"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/index.js"], "sourcesContent": ["// Modern UI Components\nexport { default as But<PERSON> } from './Button';\nexport { default as Card } from './Card';\nexport { default as Input } from './Input';\nexport { default as Loading } from './Loading';\n\n// Quiz Components\nexport { default as QuizCard, QuizGrid } from './QuizCard';\nexport { default as QuizQuestion } from './QuizQuestion';\nexport { default as QuizTimer, QuizTimerOverlay } from './QuizTimer';\n\n// Theme & Performance Components\nexport { default as ThemeToggle, ThemeSwitch, ThemeToggleWithLabel } from './ThemeToggle';\nexport { default as LazyImage } from './LazyImage';\nexport { default as ErrorBoundary, ErrorFallback } from './ErrorBoundary';\n\n// Responsive Components\nexport {\n  default as ResponsiveContainer,\n  ResponsiveGrid,\n  ResponsiveText,\n  MobileFirst,\n  DesktopFirst,\n  ResponsiveStack,\n  ResponsiveShow\n} from './ResponsiveContainer';\n\n// Performance Components (PerformanceIndicator removed)\nexport {\n  usePerformanceMonitor,\n  LazyWrapper,\n  OptimizedImage,\n  useDebouncedSearch,\n  VirtualList\n} from './PerformanceMonitor';\n\n// Theme Context\nexport { ThemeProvider, useTheme } from '../../contexts/ThemeContext';\n"], "mappings": "AAAA;AACA,SAASA,OAAO,IAAIC,MAAM,QAAQ,UAAU;AAC5C,SAASD,OAAO,IAAIE,IAAI,QAAQ,QAAQ;AACxC,SAASF,OAAO,IAAIG,KAAK,QAAQ,SAAS;AAC1C,SAASH,OAAO,IAAII,OAAO,QAAQ,WAAW;;AAE9C;AACA,SAASJ,OAAO,IAAIK,QAAQ,EAAEC,QAAQ,QAAQ,YAAY;AAC1D,SAASN,OAAO,IAAIO,YAAY,QAAQ,gBAAgB;AACxD,SAASP,OAAO,IAAIQ,SAAS,EAAEC,gBAAgB,QAAQ,aAAa;;AAEpE;AACA,SAAST,OAAO,IAAIU,WAAW,EAAEC,WAAW,EAAEC,oBAAoB,QAAQ,eAAe;AACzF,SAASZ,OAAO,IAAIa,SAAS,QAAQ,aAAa;AAClD,SAASb,OAAO,IAAIc,aAAa,EAAEC,aAAa,QAAQ,iBAAiB;;AAEzE;AACA,SACEf,OAAO,IAAIgB,mBAAmB,EAC9BC,cAAc,EACdC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,eAAe,EACfC,cAAc,QACT,uBAAuB;;AAE9B;AACA,SACEC,qBAAqB,EACrBC,WAAW,EACXC,cAAc,EACdC,kBAAkB,EAClBC,WAAW,QACN,sBAAsB;;AAE7B;AACA,SAASC,aAAa,EAAEC,QAAQ,QAAQ,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}