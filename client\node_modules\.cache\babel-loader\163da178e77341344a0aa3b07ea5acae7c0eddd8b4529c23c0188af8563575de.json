{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { getUserInfo } from \"../apicalls/users\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { SetUser } from \"../redux/usersSlice.js\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\nimport \"./ProtectedRoute.css\";\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  var _user$name, _user$name2, _user$name2$charAt, _user$name3;\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\n  const intervalRef = useRef(null);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const {\n    paymentVerificationNeeded\n  } = useSelector(state => state.payment);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const activeRoute = location.pathname;\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        dispatch(SetUser(response.data));\n      } else {\n        message.error(response.message);\n        navigate(\"/login\");\n      }\n    } catch (error) {\n      navigate(\"/login\");\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      getUserData();\n    } else {\n      navigate(\"/login\");\n    }\n  }, []);\n  useEffect(() => {\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\n      navigate('/user/plans');\n    }\n  }, [isPaymentPending, activeRoute, navigate]);\n  const verifyPaymentStatus = async () => {\n    try {\n      const data = await checkPaymentStatus();\n      console.log(\"Payment Status:\", data);\n      if (data !== null && data !== void 0 && data.error || (data === null || data === void 0 ? void 0 : data.paymentStatus) !== 'paid') {\n        if (subscriptionData !== null) {\n          dispatch(SetSubscription(null));\n        }\n        setIsPaymentPending(true);\n      } else {\n        setIsPaymentPending(false);\n        dispatch(SetSubscription(data));\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n        }\n      }\n    } catch (error) {\n      console.log(\"Error checking payment status:\", error);\n      dispatch(SetSubscription(null));\n      setIsPaymentPending(true);\n    }\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing 2222222...\");\n      if (paymentVerificationNeeded) {\n        console.log('Inside timer in effect 2....');\n        intervalRef.current = setInterval(() => {\n          console.log('Timer in action...');\n          verifyPaymentStatus();\n        }, 15000);\n        dispatch(setPaymentVerificationNeeded(false));\n      }\n    }\n  }, [paymentVerificationNeeded]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing...\");\n      verifyPaymentStatus();\n    }\n  }, [user, activeRoute]);\n  const getButtonClass = title => {\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\n      return \"\"; // No class applied\n    }\n\n    return (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" && user !== null && user !== void 0 && user.paymentRequired ? \"button-disabled\" : \"\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout-modern min-h-screen flex flex-col\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col min-h-screen\",\n      children: [/*#__PURE__*/_jsxDEV(motion.header, {\n        initial: {\n          y: -20,\n          opacity: 0\n        },\n        animate: {\n          y: 0,\n          opacity: 1\n        },\n        className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => navigate(\"/user/hub\"),\n                className: \"hub-button flex items-center space-x-1 xs:space-x-2 px-2 xs:px-3 sm:px-4 py-1.5 xs:py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl text-xs xs:text-sm sm:text-base\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"ri-apps-2-line text-sm xs:text-base sm:text-lg\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden xs:inline\",\n                  children: \"Hub\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 flex justify-center px-2 xs:px-4\",\n              children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"brainwave-heading text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent truncate max-w-full\",\n                children: \"BrainWave\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 xs:space-x-2 sm:space-x-3 flex-shrink-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-info-desktop text-right hidden md:block\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm lg:text-base font-semibold text-gray-900 truncate max-w-32 lg:max-w-40\",\n                  children: user === null || user === void 0 ? void 0 : user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs lg:text-sm text-gray-600 font-medium\",\n                  children: user !== null && user !== void 0 && user.isAdmin ? \"Administrator\" : `Class ${user === null || user === void 0 ? void 0 : user.class} Student`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-info-tablet text-right hidden sm:block md:hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-semibold text-gray-900 truncate max-w-24\",\n                  children: user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.split(' ')[0]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-600 font-medium\",\n                  children: user !== null && user !== void 0 && user.isAdmin ? \"Admin\" : `Class ${user === null || user === void 0 ? void 0 : user.class}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-profile-container flex flex-col items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-avatar w-8 h-8 xs:w-9 xs:h-9 sm:w-10 sm:h-10 md:w-11 md:h-11 lg:w-12 lg:h-12 rounded-full overflow-hidden flex items-center justify-center border-2 border-white shadow-lg relative\",\n                  children: user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: user.profileImage,\n                    alt: \"Profile\",\n                    className: \"w-full h-full object-cover\",\n                    onError: e => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full h-full flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold text-xs xs:text-sm sm:text-base\",\n                    children: (user === null || user === void 0 ? void 0 : (_user$name2 = user.name) === null || _user$name2 === void 0 ? void 0 : (_user$name2$charAt = _user$name2.charAt(0)) === null || _user$name2$charAt === void 0 ? void 0 : _user$name2$charAt.toUpperCase()) || 'U'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-info-mobile text-center sm:hidden mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs font-semibold text-gray-900 leading-tight truncate max-w-16 xs:max-w-20\",\n                    children: (user === null || user === void 0 ? void 0 : (_user$name3 = user.name) === null || _user$name3 === void 0 ? void 0 : _user$name3.split(' ')[0]) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-600 font-medium leading-tight\",\n                    children: user !== null && user !== void 0 && user.isAdmin ? \"Admin\" : `C${user === null || user === void 0 ? void 0 : user.class}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3,\n            delay: 0.1\n          },\n          className: \"h-full\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n}\n_s(ProtectedRoute, \"cwn50ZKVcb7yRGicmnB70bd2a+Y=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch, useNavigate, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useRef", "motion", "getUserInfo", "useDispatch", "useSelector", "SetUser", "useNavigate", "useLocation", "HideLoading", "ShowLoading", "checkPaymentStatus", "SetSubscription", "setPaymentVerificationNeeded", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "_user$name", "_user$name2", "_user$name2$charAt", "_user$name3", "user", "state", "isPaymentPending", "setIsPaymentPending", "intervalRef", "subscriptionData", "subscription", "paymentVerificationNeeded", "payment", "dispatch", "navigate", "location", "activeRoute", "pathname", "getUserData", "response", "success", "data", "error", "token", "localStorage", "getItem", "includes", "verifyPaymentStatus", "console", "log", "paymentStatus", "current", "clearInterval", "paymentRequired", "isAdmin", "setInterval", "getButtonClass", "title", "className", "header", "initial", "y", "opacity", "animate", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "class", "split", "profileImage", "src", "alt", "onError", "e", "target", "style", "display", "nextS<PERSON>ling", "char<PERSON>t", "toUpperCase", "div", "transition", "duration", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\n\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const activeRoute = location.pathname;\r\n\r\n\r\n\r\n\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n      } else {\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (token) {\r\n      getUserData();\r\n    } else {\r\n      navigate(\"/login\");\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\r\n      navigate('/user/plans');\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        setIsPaymentPending(true);\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      setIsPaymentPending(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing 2222222...\");\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    }\r\n  }, [paymentVerificationNeeded]);\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing...\");\r\n      verifyPaymentStatus();\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\r\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    return subscriptionData?.paymentStatus !== \"paid\" && user?.paymentRequired\r\n      ? \"button-disabled\"\r\n      : \"\";\r\n  };\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex flex-col\">\r\n      {/* No sidebar - users will use hub for navigation */}\r\n\r\n\r\n      {/* Main Content Area */}\r\n      <div className=\"flex-1 flex flex-col min-h-screen\">\r\n        {/* Modern Responsive Header */}\r\n        <motion.header\r\n          initial={{ y: -20, opacity: 0 }}\r\n          animate={{ y: 0, opacity: 1 }}\r\n          className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\"\r\n        >\r\n          <div className=\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n            <div className=\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\">\r\n              {/* Left Section - Hub Button */}\r\n              <div className=\"flex items-center flex-shrink-0\">\r\n                <button\r\n                  onClick={() => navigate(\"/user/hub\")}\r\n                  className=\"hub-button flex items-center space-x-1 xs:space-x-2 px-2 xs:px-3 sm:px-4 py-1.5 xs:py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl text-xs xs:text-sm sm:text-base\"\r\n                >\r\n                  <i className=\"ri-apps-2-line text-sm xs:text-base sm:text-lg\"></i>\r\n                  <span className=\"hidden xs:inline\">Hub</span>\r\n                </button>\r\n              </div>\r\n\r\n              {/* Center Section - Brainwave Heading */}\r\n              <div className=\"flex-1 flex justify-center px-2 xs:px-4\">\r\n                <h1 className=\"brainwave-heading text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent truncate max-w-full\">\r\n                  BrainWave\r\n                </h1>\r\n              </div>\r\n\r\n              {/* Right Section - User Profile */}\r\n              <div className=\"flex items-center space-x-1 xs:space-x-2 sm:space-x-3 flex-shrink-0\">\r\n                {/* Desktop/Tablet: Show user info beside avatar */}\r\n                <div className=\"user-info-desktop text-right hidden md:block\">\r\n                  <div className=\"text-sm lg:text-base font-semibold text-gray-900 truncate max-w-32 lg:max-w-40\">\r\n                    {user?.name}\r\n                  </div>\r\n                  <div className=\"text-xs lg:text-sm text-gray-600 font-medium\">\r\n                    {user?.isAdmin ? \"Administrator\" : `Class ${user?.class} Student`}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Tablet: Show abbreviated user info */}\r\n                <div className=\"user-info-tablet text-right hidden sm:block md:hidden\">\r\n                  <div className=\"text-sm font-semibold text-gray-900 truncate max-w-24\">\r\n                    {user?.name?.split(' ')[0]}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-600 font-medium\">\r\n                    {user?.isAdmin ? \"Admin\" : `Class ${user?.class}`}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* User Profile Avatar */}\r\n                <div className=\"user-profile-container flex flex-col items-center\">\r\n                  <div className=\"user-avatar w-8 h-8 xs:w-9 xs:h-9 sm:w-10 sm:h-10 md:w-11 md:h-11 lg:w-12 lg:h-12 rounded-full overflow-hidden flex items-center justify-center border-2 border-white shadow-lg relative\">\r\n                    {user?.profileImage ? (\r\n                      <img\r\n                        src={user.profileImage}\r\n                        alt=\"Profile\"\r\n                        className=\"w-full h-full object-cover\"\r\n                        onError={(e) => {\r\n                          e.target.style.display = 'none';\r\n                          e.target.nextSibling.style.display = 'flex';\r\n                        }}\r\n                      />\r\n                    ) : (\r\n                      <div className=\"w-full h-full flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold text-xs xs:text-sm sm:text-base\">\r\n                        {user?.name?.charAt(0)?.toUpperCase() || 'U'}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Mobile: Show name and class below avatar */}\r\n                  <div className=\"user-info-mobile text-center sm:hidden mt-1\">\r\n                    <div className=\"text-xs font-semibold text-gray-900 leading-tight truncate max-w-16 xs:max-w-20\">\r\n                      {user?.name?.split(' ')[0] || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-600 font-medium leading-tight\">\r\n                      {user?.isAdmin ? \"Admin\" : `C${user?.class}`}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n\r\n        {/* Page Content */}\r\n        <main className=\"flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.1 }}\r\n            className=\"h-full\"\r\n          >\r\n            {children}\r\n          </motion.div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,sBAAsB;AAC/D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAO,sBAAsB;AAC7B,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,4BAA4B,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGxE,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,kBAAA,EAAAC,WAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM2B,WAAW,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM;IAAE2B;EAAiB,CAAC,GAAGvB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACK,YAAY,CAAC;EACvE,MAAM;IAAEC;EAA0B,CAAC,GAAGzB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACO,OAAO,CAAC;EAC3E,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,WAAW,GAAGD,QAAQ,CAACE,QAAQ;EAMrC,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnC,WAAW,CAAC,CAAC;MACpC,IAAImC,QAAQ,CAACC,OAAO,EAAE;QACpBP,QAAQ,CAAC1B,OAAO,CAACgC,QAAQ,CAACE,IAAI,CAAC,CAAC;MAClC,CAAC,MAAM;QACL3C,OAAO,CAAC4C,KAAK,CAACH,QAAQ,CAACzC,OAAO,CAAC;QAC/BoC,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdR,QAAQ,CAAC,QAAQ,CAAC;MAClBpC,OAAO,CAAC4C,KAAK,CAACA,KAAK,CAAC5C,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDE,SAAS,CAAC,MAAM;IACd,MAAM2C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTL,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLJ,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;EAINlC,SAAS,CAAC,MAAM;IACd,IAAI0B,gBAAgB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAACoB,QAAQ,CAACV,WAAW,CAAC,EAAE;MACrEF,QAAQ,CAAC,aAAa,CAAC;IACzB;EACF,CAAC,EAAE,CAACR,gBAAgB,EAAEU,WAAW,EAAEF,QAAQ,CAAC,CAAC;EAE7C,MAAMa,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMN,IAAI,GAAG,MAAM7B,kBAAkB,CAAC,CAAC;MACvCoC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAER,IAAI,CAAC;MACpC,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,KAAK,IAAI,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,aAAa,MAAK,MAAM,EAAE;QACjD,IAAIrB,gBAAgB,KAAK,IAAI,EAAE;UAC7BI,QAAQ,CAACpB,eAAe,CAAC,IAAI,CAAC,CAAC;QACjC;QACAc,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MACI;QACHA,mBAAmB,CAAC,KAAK,CAAC;QAC1BM,QAAQ,CAACpB,eAAe,CAAC4B,IAAI,CAAC,CAAC;QAC/B,IAAIb,WAAW,CAACuB,OAAO,EAAE;UACvBC,aAAa,CAACxB,WAAW,CAACuB,OAAO,CAAC;QACpC;MACF;IACF,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdM,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEP,KAAK,CAAC;MACpDT,QAAQ,CAACpB,eAAe,CAAC,IAAI,CAAC,CAAC;MAC/Bc,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED3B,SAAS,CAAC,MAAM;IACd,IAAIwB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,IAAI,EAAC7B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAE;MAC3CN,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MAEvC,IAAIlB,yBAAyB,EAAE;QAC7BiB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CrB,WAAW,CAACuB,OAAO,GAAGI,WAAW,CAAC,MAAM;UACtCP,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCF,mBAAmB,CAAC,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC;QACTd,QAAQ,CAACnB,4BAA4B,CAAC,KAAK,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,EAAE,CAACiB,yBAAyB,CAAC,CAAC;EAE/B/B,SAAS,CAAC,MAAM;IACd,IAAIwB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,IAAI,EAAC7B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAE;MAC3CN,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BF,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACvB,IAAI,EAAEY,WAAW,CAAC,CAAC;EAGvB,MAAMoB,cAAc,GAAIC,KAAK,IAAK;IAChC;IACA,IAAI,CAACjC,IAAI,CAAC6B,eAAe,IAAII,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,QAAQ,EAAE;MAC3F,OAAO,EAAE,CAAC,CAAC;IACb;;IAEA,OAAO,CAAA5B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqB,aAAa,MAAK,MAAM,IAAI1B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,GACtE,iBAAiB,GACjB,EAAE;EACR,CAAC;EAKD,oBACErC,OAAA;IAAK0C,SAAS,EAAC,0CAA0C;IAAAxC,QAAA,eAKvDF,OAAA;MAAK0C,SAAS,EAAC,mCAAmC;MAAAxC,QAAA,gBAEhDF,OAAA,CAACb,MAAM,CAACwD,MAAM;QACZC,OAAO,EAAE;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QAChCC,OAAO,EAAE;UAAEF,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAC9BJ,SAAS,EAAC,kKAAkK;QAAAxC,QAAA,eAE5KF,OAAA;UAAK0C,SAAS,EAAC,+CAA+C;UAAAxC,QAAA,eAC5DF,OAAA;YAAK0C,SAAS,EAAC,wEAAwE;YAAAxC,QAAA,gBAErFF,OAAA;cAAK0C,SAAS,EAAC,iCAAiC;cAAAxC,QAAA,eAC9CF,OAAA;gBACEgD,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,WAAW,CAAE;gBACrCwB,SAAS,EAAC,8TAA8T;gBAAAxC,QAAA,gBAExUF,OAAA;kBAAG0C,SAAS,EAAC;gBAAgD;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClEpD,OAAA;kBAAM0C,SAAS,EAAC,kBAAkB;kBAAAxC,QAAA,EAAC;gBAAG;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNpD,OAAA;cAAK0C,SAAS,EAAC,yCAAyC;cAAAxC,QAAA,eACtDF,OAAA;gBAAI0C,SAAS,EAAC,iLAAiL;gBAAAxC,QAAA,EAAC;cAEhM;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGNpD,OAAA;cAAK0C,SAAS,EAAC,qEAAqE;cAAAxC,QAAA,gBAElFF,OAAA;gBAAK0C,SAAS,EAAC,8CAA8C;gBAAAxC,QAAA,gBAC3DF,OAAA;kBAAK0C,SAAS,EAAC,gFAAgF;kBAAAxC,QAAA,EAC5FM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACNpD,OAAA;kBAAK0C,SAAS,EAAC,8CAA8C;kBAAAxC,QAAA,EAC1DM,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAG,eAAe,GAAI,SAAQ9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,KAAM;gBAAS;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNpD,OAAA;gBAAK0C,SAAS,EAAC,uDAAuD;gBAAAxC,QAAA,gBACpEF,OAAA;kBAAK0C,SAAS,EAAC,uDAAuD;kBAAAxC,QAAA,EACnEM,IAAI,aAAJA,IAAI,wBAAAJ,UAAA,GAAJI,IAAI,CAAE6C,IAAI,cAAAjD,UAAA,uBAAVA,UAAA,CAAYmD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACNpD,OAAA;kBAAK0C,SAAS,EAAC,mCAAmC;kBAAAxC,QAAA,EAC/CM,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAG,OAAO,GAAI,SAAQ9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,KAAM;gBAAC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNpD,OAAA;gBAAK0C,SAAS,EAAC,mDAAmD;gBAAAxC,QAAA,gBAChEF,OAAA;kBAAK0C,SAAS,EAAC,0LAA0L;kBAAAxC,QAAA,EACtMM,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgD,YAAY,gBACjBxD,OAAA;oBACEyD,GAAG,EAAEjD,IAAI,CAACgD,YAAa;oBACvBE,GAAG,EAAC,SAAS;oBACbhB,SAAS,EAAC,4BAA4B;oBACtCiB,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;sBAC/BH,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFpD,OAAA;oBAAK0C,SAAS,EAAC,oJAAoJ;oBAAAxC,QAAA,EAChK,CAAAM,IAAI,aAAJA,IAAI,wBAAAH,WAAA,GAAJG,IAAI,CAAE6C,IAAI,cAAAhD,WAAA,wBAAAC,kBAAA,GAAVD,WAAA,CAAY4D,MAAM,CAAC,CAAC,CAAC,cAAA3D,kBAAA,uBAArBA,kBAAA,CAAuB4D,WAAW,CAAC,CAAC,KAAI;kBAAG;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNpD,OAAA;kBAAK0C,SAAS,EAAC,6CAA6C;kBAAAxC,QAAA,gBAC1DF,OAAA;oBAAK0C,SAAS,EAAC,iFAAiF;oBAAAxC,QAAA,EAC7F,CAAAM,IAAI,aAAJA,IAAI,wBAAAD,WAAA,GAAJC,IAAI,CAAE6C,IAAI,cAAA9C,WAAA,uBAAVA,WAAA,CAAYgD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI;kBAAM;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACNpD,OAAA;oBAAK0C,SAAS,EAAC,iDAAiD;oBAAAxC,QAAA,EAC7DM,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAG,OAAO,GAAI,IAAG9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,KAAM;kBAAC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGhBpD,OAAA;QAAM0C,SAAS,EAAC,gEAAgE;QAAAxC,QAAA,eAC9EF,OAAA,CAACb,MAAM,CAACgF,GAAG;UACTvB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BuB,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1C5B,SAAS,EAAC,QAAQ;UAAAxC,QAAA,EAEjBA;QAAQ;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjD,EAAA,CApNQF,cAAc;EAAA,QACJX,WAAW,EAGCA,WAAW,EACFA,WAAW,EAChCD,WAAW,EACXG,WAAW,EACXC,WAAW;AAAA;AAAA8E,EAAA,GARrBtE,cAAc;AAsNvB,eAAeA,cAAc;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}