{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\WriteExam\\\\index.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useCallback, useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getExamById } from \"../../../apicalls/exams\";\nimport { addReport } from \"../../../apicalls/reports\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport Instructions from \"./Instructions\";\nimport Pass from \"../../../assets/pass.gif\";\nimport Fail from \"../../../assets/fail.gif\";\nimport Confetti from \"react-confetti\";\nimport useWindowSize from \"react-use/lib/useWindowSize\";\nimport PassSound from \"../../../assets/pass.mp3\";\nimport { QuizQuestion, QuizTimer, QuizTimerOverlay, Card, Button, Loading } from \"../../../components/modern\";\nimport { TbClock, TbQuestionMark, TbCheck, TbX, TbFlag, TbArrowLeft, TbArrowRight, TbBrain } from \"react-icons/tb\";\nimport FailSound from \"../../../assets/fail.mp3\";\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\nimport ContentRenderer from \"../../../components/ContentRenderer\";\nimport QuizRenderer from \"../../../components/QuizRenderer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction WriteExam() {\n  _s();\n  var _questions$selectedQu, _questions$selectedQu2, _questions$selectedQu3, _questions$selectedQu4, _questions$selectedQu5, _result$correctAnswer, _result$correctAnswer2, _result$correctAnswer3, _result$correctAnswer4;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [result, setResult] = useState({});\n  const params = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [view, setView] = useState(\"instructions\");\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const [explanations, setExplanations] = useState({});\n  const getExamData = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      console.log(\"Fetching exam data for ID:\", params.id);\n      const response = await getExamById({\n        examId: params.id\n      });\n      console.log(\"Exam API Response:\", response);\n      dispatch(HideLoading());\n      if (response.success) {\n        const examData = response.data;\n        const questions = (examData === null || examData === void 0 ? void 0 : examData.questions) || [];\n        console.log(\"Exam Data:\", examData);\n        console.log(\"Questions found:\", questions.length);\n        console.log(\"Questions:\", questions);\n        setQuestions(questions);\n        setExamData(examData);\n        setSecondsLeft((examData === null || examData === void 0 ? void 0 : examData.duration) || 0);\n        if (questions.length === 0) {\n          console.warn(\"No questions found in exam data\");\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\n        }\n      } else {\n        console.error(\"API Error:\", response.message);\n        message.error(response.message || \"Failed to load exam data\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      console.error(\"Exception in getExamData:\", error);\n      message.error(error.message || \"Failed to load exam. Please try again.\");\n    }\n  }, [params.id, dispatch]);\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      // Check if user is available\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      const indexMap = [];\n      questions.forEach((q, idx) => {\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          indexMap.push(idx);\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      const wrongPayload = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: q.correctAnswer || q.correctOption,\n              userAnswer: userAnswerKey\n            });\n          }\n        } else if (q.answerType === \"Options\") {\n          const correctKey = q.correctOption;\n          const correctValue = q.options && q.options[correctKey] || correctKey;\n          const userValue = q.options && q.options[userAnswerKey] || userAnswerKey || \"\";\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: correctValue,\n              userAnswer: userValue\n            });\n          }\n        }\n      });\n      const verdict = correctAnswers.length >= examData.passingMarks ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers: correctAnswers || [],\n        wrongAnswers: wrongAnswers || [],\n        verdict: verdict || \"Fail\"\n      };\n      setResult(tempResult);\n      const response = await addReport({\n        exam: params.id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        setView(\"result\");\n        window.scrollTo(0, 0);\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [question]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const startTimer = () => {\n    const totalSeconds = (examData === null || examData === void 0 ? void 0 : examData.duration) || 0;\n    setSecondsLeft(totalSeconds);\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  };\n  useEffect(() => {\n    if (timeUp && view === \"questions\") {\n      clearInterval(intervalId);\n      calculateResult();\n    }\n  }, [timeUp, view, intervalId, calculateResult]);\n  useEffect(() => {\n    if (params.id) {\n      getExamData();\n    }\n  }, [params.id, getExamData]);\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [intervalId]);\n\n  // Add fullscreen class for all quiz views (instructions, questions, results)\n  useEffect(() => {\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\n      document.body.classList.add(\"quiz-fullscreen\");\n    } else {\n      document.body.classList.remove(\"quiz-fullscreen\");\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.classList.remove(\"quiz-fullscreen\");\n    };\n  }, [view]);\n\n  // Repair function for fixing orphaned questions\n  const repairExamQuestions = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await fetch('/api/exams/repair-exam-questions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          examId: params.id\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        message.success(data.message);\n        // Reload the exam data\n        getExamData();\n      } else {\n        message.error(data.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to repair exam questions\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  // Check if user is authenticated\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-10 h-10 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-8\",\n          children: \"Please log in to access the exam and start your learning journey.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n          onClick: () => navigate(\"/login\"),\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this);\n  }\n  return examData ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [view === \"instructions\" && /*#__PURE__*/_jsxDEV(Instructions, {\n      examData: examData,\n      setView: setView,\n      startTimer: startTimer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 9\n    }, this), view === \"questions\" && (questions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-amber-800 mb-4\",\n          children: \"No Questions Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-amber-700 mb-6 text-lg leading-relaxed\",\n          children: \"This exam appears to have no questions. This could be due to:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"text-left text-amber-700 mb-8 space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Questions not properly linked to this exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Database connection issues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Exam configuration problems\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: repairExamQuestions,\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\uD83D\\uDD27 Repair Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              console.log(\"Retrying exam data fetch...\");\n              getExamData();\n            },\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\uD83D\\uDD04 Retry Loading\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/user/quiz'),\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\u2190 Back to Quiz List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container-modern\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-8 h-8 text-primary-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: (examData === null || examData === void 0 ? void 0 : examData.name) || \"Quiz\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(QuizTimer, {\n              duration: (examData === null || examData === void 0 ? void 0 : examData.duration) || 0,\n              onTimeUp: () => {\n                setTimeUp(true);\n                calculateResult();\n              },\n              isActive: !timeUp,\n              showWarning: true,\n              warningThreshold: 300\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-modern py-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.2\n          },\n          children: questions[selectedQuestionIndex] ? /*#__PURE__*/_jsxDEV(QuizQuestion, {\n            question: {\n              ...questions[selectedQuestionIndex],\n              type: ((_questions$selectedQu = questions[selectedQuestionIndex]) === null || _questions$selectedQu === void 0 ? void 0 : _questions$selectedQu.answerType) === \"Options\" ? \"mcq\" : ((_questions$selectedQu2 = questions[selectedQuestionIndex]) === null || _questions$selectedQu2 === void 0 ? void 0 : _questions$selectedQu2.answerType) === \"Free Text\" || ((_questions$selectedQu3 = questions[selectedQuestionIndex]) === null || _questions$selectedQu3 === void 0 ? void 0 : _questions$selectedQu3.answerType) === \"Fill in the Blank\" ? \"fill\" : (_questions$selectedQu4 = questions[selectedQuestionIndex]) !== null && _questions$selectedQu4 !== void 0 && _questions$selectedQu4.imageUrl ? \"image\" : \"mcq\",\n              options: (_questions$selectedQu5 = questions[selectedQuestionIndex]) !== null && _questions$selectedQu5 !== void 0 && _questions$selectedQu5.options ? Object.values(questions[selectedQuestionIndex].options).filter(option => option && option.trim()) : []\n            },\n            questionNumber: selectedQuestionIndex + 1,\n            totalQuestions: questions.length,\n            selectedAnswer: selectedOptions[selectedQuestionIndex],\n            onAnswerSelect: answer => setSelectedOptions({\n              ...selectedOptions,\n              [selectedQuestionIndex]: answer\n            }),\n            onNext: () => {\n              if (selectedQuestionIndex === questions.length - 1) {\n                calculateResult();\n              } else {\n                setSelectedQuestionIndex(selectedQuestionIndex + 1);\n              }\n            },\n            onPrevious: () => {\n              if (selectedQuestionIndex > 0) {\n                setSelectedQuestionIndex(selectedQuestionIndex - 1);\n              }\n            },\n            timeRemaining: secondsLeft,\n            isLastQuestion: selectedQuestionIndex === questions.length - 1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-8 bg-red-50 rounded-lg border border-red-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"ri-error-warning-line text-3xl text-red-600 mb-4 block\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-red-800 mb-2\",\n              children: \"Question Not Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-600\",\n              children: \"This question could not be loaded. Please try refreshing the page.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.reload(),\n              className: \"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n              children: \"Refresh Page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(QuizTimerOverlay, {\n        timeRemaining: secondsLeft,\n        onClose: () => {}\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 11\n    }, this)), view === \"result\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\",\n      children: [result.verdict === \"Pass\" && /*#__PURE__*/_jsxDEV(Confetti, {\n        width: width,\n        height: height\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 41\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-8 py-10 text-center relative ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\" : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500 to-green-600\" : \"bg-gradient-to-br from-amber-500 to-orange-600\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: result.verdict === \"Pass\" ? Pass : Fail,\n                  alt: result.verdict,\n                  className: \"w-12 h-12 object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: `text-4xl font-black mb-4 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"}`,\n                children: result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\",\n                children: result.verdict === \"Pass\" ? \"You've mastered this exam with flying colors!\" : \"Every challenge makes you stronger. Try again!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-blue-600 mb-2 tracking-tight\",\n                    children: [Math.round((((_result$correctAnswer = result.correctAnswers) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.length) || 0) / questions.length * 100), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-blue-700/80 uppercase tracking-wider\",\n                    children: \"Your Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-emerald-600 mb-2 tracking-tight\",\n                    children: [((_result$correctAnswer2 = result.correctAnswers) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.length) || 0, \"/\", questions.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\",\n                    children: \"Correct\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\" : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"} to-transparent`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-4xl font-black mb-2 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                    children: result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-sm font-bold uppercase tracking-wider ${result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"}`,\n                    children: result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 544,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative bg-slate-100 rounded-2xl p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-bold text-slate-700 mb-1\",\n                    children: \"Performance Overview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-slate-500\",\n                    children: \"Your achievement level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${result.verdict === \"Pass\" ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\" : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"}`,\n                      style: {\n                        width: `${(((_result$correctAnswer3 = result.correctAnswers) === null || _result$correctAnswer3 === void 0 ? void 0 : _result$correctAnswer3.length) || 0) / questions.length * 100}%`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 570,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 562,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"0%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-lg font-black tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                      children: [Math.round((((_result$correctAnswer4 = result.correctAnswers) === null || _result$correctAnswer4 === void 0 ? void 0 : _result$correctAnswer4.length) || 0) / questions.length * 100), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"100%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 580,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                onClick: () => setView(\"review\"),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative\",\n                  children: \"Review Answers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 9\n    }, this), view === \"review\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2\",\n              children: \"Answer Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-slate-600\",\n              children: \"Quick overview of your answers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3 mb-6\",\n          children: questions.map((question, index) => {\n            const correctAnswer = question.answerType === \"Options\" ? question.correctOption : question.correctAnswer;\n            const isCorrect = correctAnswer === selectedOptions[index];\n            const userAnswer = selectedOptions[index];\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"backdrop-blur-md rounded-lg shadow-md border-2 p-4\",\n              style: {\n                backgroundColor: isCorrect ? '#bbf7d0' : '#fecaca',\n                borderColor: isCorrect ? '#22c55e' : '#ef4444'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1\",\n                    children: index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-slate-800 font-medium leading-relaxed\",\n                      children: question.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-semibold text-slate-600\",\n                  children: \"Your Answer: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`,\n                  children: question.answerType === \"Options\" ? question.options && userAnswer && question.options[userAnswer] || userAnswer || \"Not answered\" : userAnswer || \"Not answered\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 23\n                }, this), isCorrect ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-3 text-green-600 text-2xl font-black\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-3 text-red-600 text-2xl font-black\",\n                  children: \"\\u2717\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 21\n              }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-semibold text-slate-600\",\n                  children: \"Correct Answer: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-green-700\",\n                  children: question.answerType === \"Options\" ? question.options && question.options[question.correctOption] || question.correctOption : question.correctAnswer || question.correctOption\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-3 text-green-500 text-2xl font-black\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 23\n              }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2\",\n                  onClick: () => {\n                    console.log('Fetching explanation for:', question.name);\n                    fetchExplanation(question.name, question.answerType === \"Options\" ? question.options && question.options[question.correctOption] || question.correctOption : question.correctAnswer || question.correctOption, question.answerType === \"Options\" ? question.options && question.options[userAnswer] || userAnswer || \"Not answered\" : userAnswer || \"Not answered\", question.image);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\uD83D\\uDCA1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Get Explanation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 23\n              }, this), explanations[question.name] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 p-3 bg-white rounded-lg border-l-4 border-l-blue-500 shadow-sm border border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-600 text-lg mr-2\",\n                    children: \"\\uD83D\\uDCA1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"font-bold text-gray-800 text-base\",\n                    children: \"Explanation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 705,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 25\n                }, this), (question.image || question.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3 p-2 bg-gray-50 rounded border border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700 text-sm font-medium\",\n                      children: \"\\uD83D\\uDCCA Reference Diagram:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 714,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 713,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: question.image || question.imageUrl,\n                      alt: \"Question diagram\",\n                      className: \"max-w-full max-h-48 object-contain rounded border border-gray-300\",\n                      style: {\n                        maxWidth: '350px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 717,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-800 leading-relaxed bg-gray-50 p-2 rounded\",\n                  children: /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                    text: explanations[question.name]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n            onClick: () => setView(\"result\"),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"relative\",\n              children: \"\\u2190 Back to Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n            onClick: () => {\n              // Reset exam state and restart\n              setView(\"instructions\");\n              setSelectedQuestionIndex(0);\n              setSelectedOptions({});\n              setResult({});\n              setTimeUp(false);\n              setSecondsLeft((examData === null || examData === void 0 ? void 0 : examData.duration) || 0);\n              setExplanations({});\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"relative\",\n              children: \"\\uD83D\\uDD04 Retake Quiz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 605,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 314,\n    columnNumber: 5\n  }, this) : null;\n}\n_s(WriteExam, \"1C0Nm+NIPZwSk9kt3eXZP69nUbg=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useWindowSize];\n});\n_c = WriteExam;\nexport default WriteExam;\nvar _c;\n$RefreshReg$(_c, \"WriteExam\");", "map": {"version": 3, "names": ["message", "React", "useCallback", "useEffect", "useState", "useDispatch", "useSelector", "useNavigate", "useParams", "motion", "AnimatePresence", "getExamById", "addReport", "HideLoading", "ShowLoading", "Instructions", "Pass", "Fail", "Confetti", "useWindowSize", "PassSound", "QuizQuestion", "QuizTimer", "QuizTimer<PERSON><PERSON>lay", "Card", "<PERSON><PERSON>", "Loading", "TbClock", "TbQuestionMark", "TbCheck", "TbX", "TbFlag", "TbArrowLeft", "TbArrowRight", "TbBrain", "FailSound", "chatWithChatGPTToGetAns", "chatWithChatGPTToExplainAns", "Content<PERSON><PERSON><PERSON>", "Quiz<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "WriteExam", "_s", "_questions$selectedQu", "_questions$selectedQu2", "_questions$selectedQu3", "_questions$selectedQu4", "_questions$selectedQu5", "_result$correctAnswer", "_result$correctAnswer2", "_result$correctAnswer3", "_result$correctAnswer4", "examData", "setExamData", "questions", "setQuestions", "selectedQuestionIndex", "setSelectedQuestionIndex", "selectedOptions", "setSelectedOptions", "result", "setResult", "params", "dispatch", "navigate", "view", "<PERSON><PERSON><PERSON><PERSON>", "secondsLeft", "setSecondsLeft", "timeUp", "setTimeUp", "intervalId", "setIntervalId", "isLoading", "setIsLoading", "user", "state", "width", "height", "explanations", "setExplanations", "getExamData", "console", "log", "id", "response", "examId", "success", "data", "length", "duration", "warn", "warning", "error", "checkFreeTextAnswers", "payload", "calculateResult", "_id", "freeTextPayload", "indexMap", "for<PERSON>ach", "q", "idx", "answerType", "push", "question", "name", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "userAnswer", "gptResults", "gptMap", "r", "isCorrect", "reason", "correctAnswers", "wrongAnswers", "wrongPayload", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "correctValue", "options", "userValue", "verdict", "passingMarks", "tempResult", "exam", "window", "scrollTo", "Audio", "play", "fetchExplanation", "imageUrl", "prev", "explanation", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "document", "body", "classList", "add", "remove", "repairExamQuestions", "fetch", "method", "headers", "localStorage", "getItem", "JSON", "stringify", "json", "className", "children", "fill", "viewBox", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "div", "initial", "opacity", "y", "animate", "onTimeUp", "isActive", "showWarning", "warningThreshold", "transition", "delay", "type", "Object", "values", "filter", "option", "trim", "questionNumber", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerSelect", "answer", "onNext", "onPrevious", "timeRemaining", "isLastQuestion", "location", "reload", "onClose", "src", "alt", "Math", "round", "style", "map", "index", "backgroundColor", "borderColor", "image", "max<PERSON><PERSON><PERSON>", "text", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/WriteExam/index.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { getExamById } from \"../../../apicalls/exams\";\r\nimport { addReport } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport Instructions from \"./Instructions\";\r\nimport Pass from \"../../../assets/pass.gif\";\r\nimport Fail from \"../../../assets/fail.gif\";\r\nimport Confetti from \"react-confetti\";\r\nimport useWindowSize from \"react-use/lib/useWindowSize\";\r\nimport PassSound from \"../../../assets/pass.mp3\";\r\nimport { QuizQuest<PERSON>, QuizTimer, QuizTimer<PERSON><PERSON>lay, Card, Button, Loading } from \"../../../components/modern\";\r\nimport { TbClock, TbQuestionMark, TbCheck, TbX, TbFlag, TbArrowLeft, TbArrowRight, TbBrain } from \"react-icons/tb\";\r\nimport FailSound from \"../../../assets/fail.mp3\";\r\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nimport QuizRenderer from \"../../../components/QuizRenderer\";\r\n\r\nfunction WriteExam() {\r\n  const [examData, setExamData] = useState(null);\r\n  const [questions, setQuestions] = useState([]);\r\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\r\n  const [selectedOptions, setSelectedOptions] = useState({});\r\n  const [result, setResult] = useState({});\r\n  const params = useParams();\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [view, setView] = useState(\"instructions\");\r\n  const [secondsLeft, setSecondsLeft] = useState(0);\r\n  const [timeUp, setTimeUp] = useState(false);\r\n  const [intervalId, setIntervalId] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const { width, height } = useWindowSize();\r\n  const [explanations, setExplanations] = useState({});\r\n\r\n  const getExamData = useCallback(async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      console.log(\"Fetching exam data for ID:\", params.id);\r\n\r\n      const response = await getExamById({ examId: params.id });\r\n      console.log(\"Exam API Response:\", response);\r\n\r\n      dispatch(HideLoading());\r\n\r\n      if (response.success) {\r\n        const examData = response.data;\r\n        const questions = examData?.questions || [];\r\n\r\n        console.log(\"Exam Data:\", examData);\r\n        console.log(\"Questions found:\", questions.length);\r\n        console.log(\"Questions:\", questions);\r\n\r\n        setQuestions(questions);\r\n        setExamData(examData);\r\n        setSecondsLeft(examData?.duration || 0);\r\n\r\n        if (questions.length === 0) {\r\n          console.warn(\"No questions found in exam data\");\r\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\r\n        }\r\n      } else {\r\n        console.error(\"API Error:\", response.message);\r\n        message.error(response.message || \"Failed to load exam data\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      console.error(\"Exception in getExamData:\", error);\r\n      message.error(error.message || \"Failed to load exam. Please try again.\");\r\n    }\r\n  }, [params.id, dispatch]);\r\n\r\n  const checkFreeTextAnswers = async (payload) => {\r\n    if (!payload.length) return [];\r\n    const { data } = await chatWithChatGPTToGetAns(payload);\r\n    return data;\r\n  };\r\n\r\n  const calculateResult = useCallback(async () => {\r\n    try {\r\n      // Check if user is available\r\n      if (!user || !user._id) {\r\n        message.error(\"User not found. Please log in again.\");\r\n        navigate(\"/login\");\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n\r\n      const freeTextPayload = [];\r\n      const indexMap = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          indexMap.push(idx);\r\n          freeTextPayload.push({\r\n            question: q.name,\r\n            expectedAnswer: q.correctAnswer || q.correctOption,\r\n            userAnswer: selectedOptions[idx] || \"\",\r\n          });\r\n        }\r\n      });\r\n\r\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\r\n      const gptMap = {};\r\n\r\n      gptResults.forEach((r) => {\r\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = r.result;\r\n        } else if (typeof r.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\r\n        }\r\n      });\r\n\r\n      const correctAnswers = [];\r\n      const wrongAnswers = [];\r\n      const wrongPayload = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        const userAnswerKey = selectedOptions[idx] || \"\";\r\n\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\r\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: q.correctAnswer || q.correctOption,\r\n              userAnswer: userAnswerKey,\r\n            });\r\n          }\r\n        } else if (q.answerType === \"Options\") {\r\n          const correctKey = q.correctOption;\r\n          const correctValue = (q.options && q.options[correctKey]) || correctKey;\r\n          const userValue = (q.options && q.options[userAnswerKey]) || userAnswerKey || \"\";\r\n\r\n          const isCorrect = correctKey === userAnswerKey;\r\n          const enriched = { ...q, userAnswer: userAnswerKey };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: correctValue,\r\n              userAnswer: userValue,\r\n            });\r\n          }\r\n        }\r\n      });\r\n\r\n      const verdict = correctAnswers.length >= examData.passingMarks ? \"Pass\" : \"Fail\";\r\n      const tempResult = {\r\n        correctAnswers: correctAnswers || [],\r\n        wrongAnswers: wrongAnswers || [],\r\n        verdict: verdict || \"Fail\"\r\n      };\r\n\r\n      setResult(tempResult);\r\n\r\n      const response = await addReport({\r\n        exam: params.id,\r\n        result: tempResult,\r\n        user: user._id,\r\n      });\r\n\r\n      if (response.success) {\r\n        setView(\"result\");\r\n        window.scrollTo(0, 0);\r\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\r\n\r\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\r\n      dispatch(HideLoading());\r\n\r\n      if (response.success) {\r\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\r\n      } else {\r\n        message.error(response.error || \"Failed to fetch explanation.\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const startTimer = () => {\r\n    const totalSeconds = examData?.duration || 0;\r\n    setSecondsLeft(totalSeconds);\r\n\r\n    const newIntervalId = setInterval(() => {\r\n      setSecondsLeft((prevSeconds) => {\r\n        if (prevSeconds > 0) {\r\n          return prevSeconds - 1;\r\n        } else {\r\n          setTimeUp(true);\r\n          return 0;\r\n        }\r\n      });\r\n    }, 1000);\r\n    setIntervalId(newIntervalId);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (timeUp && view === \"questions\") {\r\n      clearInterval(intervalId);\r\n      calculateResult();\r\n    }\r\n  }, [timeUp, view, intervalId, calculateResult]);\r\n\r\n  useEffect(() => {\r\n    if (params.id) {\r\n      getExamData();\r\n    }\r\n  }, [params.id, getExamData]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (intervalId) {\r\n        clearInterval(intervalId);\r\n      }\r\n    };\r\n  }, [intervalId]);\r\n\r\n  // Add fullscreen class for all quiz views (instructions, questions, results)\r\n  useEffect(() => {\r\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\r\n      document.body.classList.add(\"quiz-fullscreen\");\r\n    } else {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    };\r\n  }, [view]);\r\n\r\n  // Repair function for fixing orphaned questions\r\n  const repairExamQuestions = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await fetch('/api/exams/repair-exam-questions', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ examId: params.id })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        message.success(data.message);\r\n        // Reload the exam data\r\n        getExamData();\r\n      } else {\r\n        message.error(data.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Failed to repair exam questions\");\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  // Check if user is authenticated\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\">\r\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\">\r\n          <div className=\"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n            <svg className=\"w-10 h-10 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Authentication Required</h2>\r\n          <p className=\"text-gray-600 mb-8\">Please log in to access the exam and start your learning journey.</p>\r\n          <button\r\n            className=\"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n            onClick={() => navigate(\"/login\")}\r\n          >\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return examData ? (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n\r\n      {view === \"instructions\" && (\r\n        <Instructions\r\n          examData={examData}\r\n          setView={setView}\r\n          startTimer={startTimer}\r\n        />\r\n      )}\r\n\r\n      {view === \"questions\" && (\r\n        questions.length === 0 ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\r\n                <svg className=\"w-12 h-12 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-amber-800 mb-4\">No Questions Found</h3>\r\n              <p className=\"text-amber-700 mb-6 text-lg leading-relaxed\">\r\n                This exam appears to have no questions. This could be due to:\r\n              </p>\r\n              <ul className=\"text-left text-amber-700 mb-8 space-y-2\">\r\n                <li>• Questions not properly linked to this exam</li>\r\n                <li>• Database connection issues</li>\r\n                <li>• Exam configuration problems</li>\r\n              </ul>\r\n              <div className=\"space-y-3\">\r\n                <button\r\n                  onClick={repairExamQuestions}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔧 Repair Questions\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    console.log(\"Retrying exam data fetch...\");\r\n                    getExamData();\r\n                  }}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔄 Retry Loading\r\n                </button>\r\n                <button\r\n                  onClick={() => navigate('/user/quiz')}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  ← Back to Quiz List\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\r\n            {/* Modern Quiz Header */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              className=\"bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50\"\r\n            >\r\n              <div className=\"container-modern\">\r\n                <div className=\"flex items-center justify-between h-16\">\r\n                  {/* Quiz Info */}\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <TbBrain className=\"w-8 h-8 text-primary-600\" />\r\n                    <div>\r\n                      <h1 className=\"text-lg font-semibold text-gray-900\">{examData?.name || \"Quiz\"}</h1>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Timer */}\r\n                  <QuizTimer\r\n                    duration={examData?.duration || 0}\r\n                    onTimeUp={() => {\r\n                      setTimeUp(true);\r\n                      calculateResult();\r\n                    }}\r\n                    isActive={!timeUp}\r\n                    showWarning={true}\r\n                    warningThreshold={300}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Quiz Content */}\r\n            <div className=\"container-modern py-8\">\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.2 }}\r\n              >\r\n                {questions[selectedQuestionIndex] ? (\r\n                  <QuizQuestion\r\n                    question={{\r\n                      ...questions[selectedQuestionIndex],\r\n                      type: questions[selectedQuestionIndex]?.answerType === \"Options\" ? \"mcq\" :\r\n                            questions[selectedQuestionIndex]?.answerType === \"Free Text\" ||\r\n                            questions[selectedQuestionIndex]?.answerType === \"Fill in the Blank\" ? \"fill\" :\r\n                            questions[selectedQuestionIndex]?.imageUrl ? \"image\" : \"mcq\",\r\n                      options: questions[selectedQuestionIndex]?.options ?\r\n                        Object.values(questions[selectedQuestionIndex].options).filter(option => option && option.trim()) : []\r\n                    }}\r\n                  questionNumber={selectedQuestionIndex + 1}\r\n                  totalQuestions={questions.length}\r\n                  selectedAnswer={selectedOptions[selectedQuestionIndex]}\r\n                  onAnswerSelect={(answer) =>\r\n                    setSelectedOptions({\r\n                      ...selectedOptions,\r\n                      [selectedQuestionIndex]: answer,\r\n                    })\r\n                  }\r\n                  onNext={() => {\r\n                    if (selectedQuestionIndex === questions.length - 1) {\r\n                      calculateResult();\r\n                    } else {\r\n                      setSelectedQuestionIndex(selectedQuestionIndex + 1);\r\n                    }\r\n                  }}\r\n                    onPrevious={() => {\r\n                      if (selectedQuestionIndex > 0) {\r\n                        setSelectedQuestionIndex(selectedQuestionIndex - 1);\r\n                      }\r\n                    }}\r\n                    timeRemaining={secondsLeft}\r\n                    isLastQuestion={selectedQuestionIndex === questions.length - 1}\r\n                  />\r\n                ) : (\r\n                  <div className=\"text-center p-8 bg-red-50 rounded-lg border border-red-200\">\r\n                    <i className=\"ri-error-warning-line text-3xl text-red-600 mb-4 block\"></i>\r\n                    <h3 className=\"text-lg font-semibold text-red-800 mb-2\">Question Not Available</h3>\r\n                    <p className=\"text-red-600\">This question could not be loaded. Please try refreshing the page.</p>\r\n                    <button\r\n                      onClick={() => window.location.reload()}\r\n                      className=\"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\r\n                    >\r\n                      Refresh Page\r\n                    </button>\r\n                  </div>\r\n                )}\r\n              </motion.div>\r\n            </div>\r\n\r\n            {/* Timer Overlay for Critical Moments */}\r\n            <QuizTimerOverlay\r\n              timeRemaining={secondsLeft}\r\n              onClose={() => {}}\r\n            />\r\n          </div>\r\n        )\r\n      )}\r\n\r\n      {view === \"result\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\">\r\n          {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\r\n\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            <div className=\"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\">\r\n              {/* Modern Header */}\r\n              <div className={`px-8 py-10 text-center relative ${\r\n                result.verdict === \"Pass\"\r\n                  ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\"\r\n                  : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"\r\n              }`}>\r\n                <div className=\"relative\">\r\n                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500 to-green-600\"\r\n                      : \"bg-gradient-to-br from-amber-500 to-orange-600\"\r\n                  }`}>\r\n                    <img\r\n                      src={result.verdict === \"Pass\" ? Pass : Fail}\r\n                      alt={result.verdict}\r\n                      className=\"w-12 h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n                  <h1 className={`text-4xl font-black mb-4 tracking-tight ${\r\n                    result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"\r\n                  }`}>\r\n                    {result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"}\r\n                  </h1>\r\n                  <p className=\"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\">\r\n                    {result.verdict === \"Pass\"\r\n                      ? \"You've mastered this exam with flying colors!\"\r\n                      : \"Every challenge makes you stronger. Try again!\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modern Statistics Cards */}\r\n              <div className=\"p-8\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n                  {/* Score Card */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-blue-600 mb-2 tracking-tight\">\r\n                        {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-blue-700/80 uppercase tracking-wider\">Your Score</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Correct vs Total */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-emerald-600 mb-2 tracking-tight\">\r\n                        {result.correctAnswers?.length || 0}/{questions.length}\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\">Correct</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Pass Status */}\r\n                  <div className={`group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\"\r\n                      : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"\r\n                  }`}>\r\n                    <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${\r\n                      result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"\r\n                    } to-transparent`}></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className={`text-4xl font-black mb-2 tracking-tight ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"}\r\n                      </div>\r\n                      <div className={`text-sm font-bold uppercase tracking-wider ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Progress Visualization */}\r\n                <div className=\"mb-8\">\r\n                  <div className=\"relative bg-slate-100 rounded-2xl p-6\">\r\n                    <div className=\"text-center mb-4\">\r\n                      <h3 className=\"text-lg font-bold text-slate-700 mb-1\">Performance Overview</h3>\r\n                      <p className=\"text-sm text-slate-500\">Your achievement level</p>\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <div className=\"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\">\r\n                        <div\r\n                          className={`h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${\r\n                            result.verdict === \"Pass\"\r\n                              ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\"\r\n                              : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"\r\n                          }`}\r\n                          style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}\r\n                        >\r\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"></div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center mt-3\">\r\n                        <span className=\"text-xs font-medium text-slate-500\">0%</span>\r\n                        <span className={`text-lg font-black tracking-tight ${\r\n                          result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                        }`}>\r\n                          {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                        </span>\r\n                        <span className=\"text-xs font-medium text-slate-500\">100%</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Action Buttons */}\r\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => setView(\"review\")}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">Review Answers</span>\r\n                  </button>\r\n\r\n\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"review\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6\">\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            {/* Simple Header */}\r\n            <div className=\"text-center mb-6\">\r\n              <div className=\"bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50\">\r\n                <h2 className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2\">\r\n                  Answer Review\r\n                </h2>\r\n                <p className=\"text-slate-600\">Quick overview of your answers</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Compact Questions Review */}\r\n            <div className=\"space-y-3 mb-6\">\r\n              {questions.map((question, index) => {\r\n                const correctAnswer = question.answerType === \"Options\"\r\n                  ? question.correctOption\r\n                  : question.correctAnswer;\r\n                const isCorrect = correctAnswer === selectedOptions[index];\r\n                const userAnswer = selectedOptions[index];\r\n\r\n                return (\r\n                  <div\r\n                    key={index}\r\n                    className=\"backdrop-blur-md rounded-lg shadow-md border-2 p-4\"\r\n                    style={{\r\n                      backgroundColor: isCorrect ? '#bbf7d0' : '#fecaca',\r\n                      borderColor: isCorrect ? '#22c55e' : '#ef4444'\r\n                    }}\r\n                  >\r\n                    {/* Question */}\r\n                    <div className=\"mb-3\">\r\n                      <div className=\"flex items-start space-x-3\">\r\n                        <div className=\"w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1\">\r\n                          {index + 1}\r\n                        </div>\r\n                        <div className=\"flex-1\">\r\n                          <p className=\"text-slate-800 font-medium leading-relaxed\">{question.name}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Your Answer with Visual Indicator */}\r\n                    <div className=\"mb-2\">\r\n                      <span className=\"text-sm font-semibold text-slate-600\">Your Answer: </span>\r\n                      <span className={`font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\r\n                        {question.answerType === \"Options\"\r\n                          ? (question.options && userAnswer && question.options[userAnswer]) || userAnswer || \"Not answered\"\r\n                          : userAnswer || \"Not answered\"}\r\n                      </span>\r\n                      {isCorrect ? (\r\n                        <span className=\"ml-3 text-green-600 text-2xl font-black\">✓</span>\r\n                      ) : (\r\n                        <span className=\"ml-3 text-red-600 text-2xl font-black\">✗</span>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Correct Answer (only for wrong answers) */}\r\n                    {!isCorrect && (\r\n                      <div className=\"mb-2\">\r\n                        <span className=\"text-sm font-semibold text-slate-600\">Correct Answer: </span>\r\n                        <span className=\"font-medium text-green-700\">\r\n                          {question.answerType === \"Options\"\r\n                            ? (question.options && question.options[question.correctOption]) || question.correctOption\r\n                            : (question.correctAnswer || question.correctOption)}\r\n                        </span>\r\n                        <span className=\"ml-3 text-green-500 text-2xl font-black\">✓</span>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* See Explanation Button (only for wrong answers) */}\r\n                    {!isCorrect && (\r\n                      <div className=\"mt-2\">\r\n                        <button\r\n                          className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2\"\r\n                          onClick={() => {\r\n                            console.log('Fetching explanation for:', question.name);\r\n                            fetchExplanation(\r\n                              question.name,\r\n                              question.answerType === \"Options\"\r\n                                ? (question.options && question.options[question.correctOption]) || question.correctOption\r\n                                : (question.correctAnswer || question.correctOption),\r\n                              question.answerType === \"Options\"\r\n                                ? (question.options && question.options[userAnswer]) || userAnswer || \"Not answered\"\r\n                                : userAnswer || \"Not answered\",\r\n                              question.image\r\n                            );\r\n                          }}\r\n                        >\r\n                          <span>💡</span>\r\n                          <span>Get Explanation</span>\r\n                        </button>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Explanation */}\r\n                    {explanations[question.name] && (\r\n                      <div className=\"mt-2 p-3 bg-white rounded-lg border-l-4 border-l-blue-500 shadow-sm border border-gray-200\">\r\n                        <div className=\"flex items-center mb-2\">\r\n                          <span className=\"text-blue-600 text-lg mr-2\">💡</span>\r\n                          <h6 className=\"font-bold text-gray-800 text-base\">\r\n                            Explanation\r\n                          </h6>\r\n                        </div>\r\n\r\n                        {/* Show diagram/image for image-based questions */}\r\n                        {(question.image || question.imageUrl) && (\r\n                          <div className=\"mb-3 p-2 bg-gray-50 rounded border border-gray-200\">\r\n                            <div className=\"flex items-center mb-1\">\r\n                              <span className=\"text-gray-700 text-sm font-medium\">📊 Reference Diagram:</span>\r\n                            </div>\r\n                            <div className=\"flex justify-center\">\r\n                              <img\r\n                                src={question.image || question.imageUrl}\r\n                                alt=\"Question diagram\"\r\n                                className=\"max-w-full max-h-48 object-contain rounded border border-gray-300\"\r\n                                style={{ maxWidth: '350px' }}\r\n                              />\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n\r\n                        <div className=\"text-sm text-gray-800 leading-relaxed bg-gray-50 p-2 rounded\">\r\n                          <ContentRenderer text={explanations[question.name]} />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n\r\n            {/* Modern Navigation */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\r\n              <button\r\n                className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                onClick={() => setView(\"result\")}\r\n              >\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                <span className=\"relative\">← Back to Results</span>\r\n              </button>\r\n\r\n              <button\r\n                className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                onClick={() => {\r\n                  // Reset exam state and restart\r\n                  setView(\"instructions\");\r\n                  setSelectedQuestionIndex(0);\r\n                  setSelectedOptions({});\r\n                  setResult({});\r\n                  setTimeUp(false);\r\n                  setSecondsLeft(examData?.duration || 0);\r\n                  setExplanations({});\r\n                }}\r\n              >\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                <span className=\"relative\">🔄 Retake Quiz</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  ) : null;\r\n}\r\n\r\nexport default WriteExam;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,YAAY,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,4BAA4B;AAC7G,SAASC,OAAO,EAAEC,cAAc,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,QAAQ,gBAAgB;AAClH,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,uBAAuB,EAAEC,2BAA2B,QAAQ,wBAAwB;AAC7F,OAAOC,eAAe,MAAM,qCAAqC;AAEjE,OAAOC,YAAY,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACyD,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM2D,MAAM,GAAGvD,SAAS,CAAC,CAAC;EAC1B,MAAMwD,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM4D,QAAQ,GAAG1D,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2D,IAAI,EAAEC,OAAO,CAAC,GAAG/D,QAAQ,CAAC,cAAc,CAAC;EAChD,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkE,MAAM,EAAEC,SAAS,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACsE,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM;IAAEwE;EAAK,CAAC,GAAGtE,WAAW,CAAEuE,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM;IAAEE,KAAK;IAAEC;EAAO,CAAC,GAAG5D,aAAa,CAAC,CAAC;EACzC,MAAM,CAAC6D,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEpD,MAAM8E,WAAW,GAAGhF,WAAW,CAAC,YAAY;IAC1C,IAAI;MACF8D,QAAQ,CAAClD,WAAW,CAAC,CAAC,CAAC;MACvBqE,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAErB,MAAM,CAACsB,EAAE,CAAC;MAEpD,MAAMC,QAAQ,GAAG,MAAM3E,WAAW,CAAC;QAAE4E,MAAM,EAAExB,MAAM,CAACsB;MAAG,CAAC,CAAC;MACzDF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,QAAQ,CAAC;MAE3CtB,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIyE,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMnC,QAAQ,GAAGiC,QAAQ,CAACG,IAAI;QAC9B,MAAMlC,SAAS,GAAG,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,SAAS,KAAI,EAAE;QAE3C4B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE/B,QAAQ,CAAC;QACnC8B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE7B,SAAS,CAACmC,MAAM,CAAC;QACjDP,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE7B,SAAS,CAAC;QAEpCC,YAAY,CAACD,SAAS,CAAC;QACvBD,WAAW,CAACD,QAAQ,CAAC;QACrBgB,cAAc,CAAC,CAAAhB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsC,QAAQ,KAAI,CAAC,CAAC;QAEvC,IAAIpC,SAAS,CAACmC,MAAM,KAAK,CAAC,EAAE;UAC1BP,OAAO,CAACS,IAAI,CAAC,iCAAiC,CAAC;UAC/C5F,OAAO,CAAC6F,OAAO,CAAC,6DAA6D,CAAC;QAChF;MACF,CAAC,MAAM;QACLV,OAAO,CAACW,KAAK,CAAC,YAAY,EAAER,QAAQ,CAACtF,OAAO,CAAC;QAC7CA,OAAO,CAAC8F,KAAK,CAACR,QAAQ,CAACtF,OAAO,IAAI,0BAA0B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO8F,KAAK,EAAE;MACd9B,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;MACvBsE,OAAO,CAACW,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD9F,OAAO,CAAC8F,KAAK,CAACA,KAAK,CAAC9F,OAAO,IAAI,wCAAwC,CAAC;IAC1E;EACF,CAAC,EAAE,CAAC+D,MAAM,CAACsB,EAAE,EAAErB,QAAQ,CAAC,CAAC;EAEzB,MAAM+B,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAACN,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAED;IAAK,CAAC,GAAG,MAAMrD,uBAAuB,CAAC4D,OAAO,CAAC;IACvD,OAAOP,IAAI;EACb,CAAC;EAED,MAAMQ,eAAe,GAAG/F,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF;MACA,IAAI,CAAC0E,IAAI,IAAI,CAACA,IAAI,CAACsB,GAAG,EAAE;QACtBlG,OAAO,CAAC8F,KAAK,CAAC,sCAAsC,CAAC;QACrD7B,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAD,QAAQ,CAAClD,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMqF,eAAe,GAAG,EAAE;MAC1B,MAAMC,QAAQ,GAAG,EAAE;MAEnB7C,SAAS,CAAC8C,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,IAAID,CAAC,CAACE,UAAU,KAAK,WAAW,IAAIF,CAAC,CAACE,UAAU,KAAK,mBAAmB,EAAE;UACxEJ,QAAQ,CAACK,IAAI,CAACF,GAAG,CAAC;UAClBJ,eAAe,CAACM,IAAI,CAAC;YACnBC,QAAQ,EAAEJ,CAAC,CAACK,IAAI;YAChBC,cAAc,EAAEN,CAAC,CAACO,aAAa,IAAIP,CAAC,CAACQ,aAAa;YAClDC,UAAU,EAAEpD,eAAe,CAAC4C,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMS,UAAU,GAAG,MAAMjB,oBAAoB,CAACI,eAAe,CAAC;MAC9D,MAAMc,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACX,OAAO,CAAEa,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACrD,MAAM,IAAI,OAAOqD,CAAC,CAACrD,MAAM,CAACsD,SAAS,KAAK,SAAS,EAAE;UACvDF,MAAM,CAACC,CAAC,CAACR,QAAQ,CAAC,GAAGQ,CAAC,CAACrD,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAOqD,CAAC,CAACC,SAAS,KAAK,SAAS,EAAE;UAC3CF,MAAM,CAACC,CAAC,CAACR,QAAQ,CAAC,GAAG;YAAES,SAAS,EAAED,CAAC,CAACC,SAAS;YAAEC,MAAM,EAAEF,CAAC,CAACE,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MACvB,MAAMC,YAAY,GAAG,EAAE;MAEvBhE,SAAS,CAAC8C,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,MAAMiB,aAAa,GAAG7D,eAAe,CAAC4C,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAID,CAAC,CAACE,UAAU,KAAK,WAAW,IAAIF,CAAC,CAACE,UAAU,KAAK,mBAAmB,EAAE;UACxE,MAAM;YAAEW,SAAS,GAAG,KAAK;YAAEC,MAAM,GAAG;UAAG,CAAC,GAAGH,MAAM,CAACX,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAMc,QAAQ,GAAG;YAAE,GAAGnB,CAAC;YAAES,UAAU,EAAES,aAAa;YAAEJ;UAAO,CAAC;UAE5D,IAAID,SAAS,EAAE;YACbE,cAAc,CAACZ,IAAI,CAACgB,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACb,IAAI,CAACgB,QAAQ,CAAC;YAC3BF,YAAY,CAACd,IAAI,CAAC;cAChBC,QAAQ,EAAEJ,CAAC,CAACK,IAAI;cAChBC,cAAc,EAAEN,CAAC,CAACO,aAAa,IAAIP,CAAC,CAACQ,aAAa;cAClDC,UAAU,EAAES;YACd,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IAAIlB,CAAC,CAACE,UAAU,KAAK,SAAS,EAAE;UACrC,MAAMkB,UAAU,GAAGpB,CAAC,CAACQ,aAAa;UAClC,MAAMa,YAAY,GAAIrB,CAAC,CAACsB,OAAO,IAAItB,CAAC,CAACsB,OAAO,CAACF,UAAU,CAAC,IAAKA,UAAU;UACvE,MAAMG,SAAS,GAAIvB,CAAC,CAACsB,OAAO,IAAItB,CAAC,CAACsB,OAAO,CAACJ,aAAa,CAAC,IAAKA,aAAa,IAAI,EAAE;UAEhF,MAAML,SAAS,GAAGO,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGnB,CAAC;YAAES,UAAU,EAAES;UAAc,CAAC;UAEpD,IAAIL,SAAS,EAAE;YACbE,cAAc,CAACZ,IAAI,CAACgB,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACb,IAAI,CAACgB,QAAQ,CAAC;YAC3BF,YAAY,CAACd,IAAI,CAAC;cAChBC,QAAQ,EAAEJ,CAAC,CAACK,IAAI;cAChBC,cAAc,EAAEe,YAAY;cAC5BZ,UAAU,EAAEc;YACd,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;MAEF,MAAMC,OAAO,GAAGT,cAAc,CAAC3B,MAAM,IAAIrC,QAAQ,CAAC0E,YAAY,GAAG,MAAM,GAAG,MAAM;MAChF,MAAMC,UAAU,GAAG;QACjBX,cAAc,EAAEA,cAAc,IAAI,EAAE;QACpCC,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCQ,OAAO,EAAEA,OAAO,IAAI;MACtB,CAAC;MAEDhE,SAAS,CAACkE,UAAU,CAAC;MAErB,MAAM1C,QAAQ,GAAG,MAAM1E,SAAS,CAAC;QAC/BqH,IAAI,EAAElE,MAAM,CAACsB,EAAE;QACfxB,MAAM,EAAEmE,UAAU;QAClBpD,IAAI,EAAEA,IAAI,CAACsB;MACb,CAAC,CAAC;MAEF,IAAIZ,QAAQ,CAACE,OAAO,EAAE;QACpBrB,OAAO,CAAC,QAAQ,CAAC;QACjB+D,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,IAAIC,KAAK,CAACN,OAAO,KAAK,MAAM,GAAG1G,SAAS,GAAGe,SAAS,CAAC,CAACkG,IAAI,CAAC,CAAC;MAC9D,CAAC,MAAM;QACLrI,OAAO,CAAC8F,KAAK,CAACR,QAAQ,CAACtF,OAAO,CAAC;MACjC;MACAgE,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAOiF,KAAK,EAAE;MACd9B,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;MACvBb,OAAO,CAAC8F,KAAK,CAACA,KAAK,CAAC9F,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACuD,SAAS,EAAEI,eAAe,EAAEN,QAAQ,EAAEU,MAAM,CAACsB,EAAE,EAAET,IAAI,EAAEX,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE/E,MAAMsE,gBAAgB,GAAG,MAAAA,CAAO5B,QAAQ,EAAEE,cAAc,EAAEG,UAAU,EAAEwB,QAAQ,KAAK;IACjF,IAAI;MACFvE,QAAQ,CAAClD,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMwE,QAAQ,GAAG,MAAMjD,2BAA2B,CAAC;QAAEqE,QAAQ;QAAEE,cAAc;QAAEG,UAAU;QAAEwB;MAAS,CAAC,CAAC;MACtGvE,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIyE,QAAQ,CAACE,OAAO,EAAE;QACpBP,eAAe,CAAEuD,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAAC9B,QAAQ,GAAGpB,QAAQ,CAACmD;QAAY,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACLzI,OAAO,CAAC8F,KAAK,CAACR,QAAQ,CAACQ,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd9B,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;MACvBb,OAAO,CAAC8F,KAAK,CAACA,KAAK,CAAC9F,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAM0I,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,YAAY,GAAG,CAAAtF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsC,QAAQ,KAAI,CAAC;IAC5CtB,cAAc,CAACsE,YAAY,CAAC;IAE5B,MAAMC,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtCxE,cAAc,CAAEyE,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACLvE,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACRE,aAAa,CAACmE,aAAa,CAAC;EAC9B,CAAC;EAEDzI,SAAS,CAAC,MAAM;IACd,IAAImE,MAAM,IAAIJ,IAAI,KAAK,WAAW,EAAE;MAClC6E,aAAa,CAACvE,UAAU,CAAC;MACzByB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC3B,MAAM,EAAEJ,IAAI,EAAEM,UAAU,EAAEyB,eAAe,CAAC,CAAC;EAE/C9F,SAAS,CAAC,MAAM;IACd,IAAI4D,MAAM,CAACsB,EAAE,EAAE;MACbH,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACnB,MAAM,CAACsB,EAAE,EAAEH,WAAW,CAAC,CAAC;EAE5B/E,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIqE,UAAU,EAAE;QACduE,aAAa,CAACvE,UAAU,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACArE,SAAS,CAAC,MAAM;IACd,IAAI+D,IAAI,KAAK,cAAc,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACxE8E,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChD,CAAC,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD;;IAEA;IACA,OAAO,MAAM;MACXJ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,CAAClF,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMmF,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFrF,QAAQ,CAAClD,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMwE,QAAQ,GAAG,MAAMgE,KAAK,CAAC,kCAAkC,EAAE;QAC/DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACDT,IAAI,EAAEU,IAAI,CAACC,SAAS,CAAC;UAAErE,MAAM,EAAExB,MAAM,CAACsB;QAAG,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMI,IAAI,GAAG,MAAMH,QAAQ,CAACuE,IAAI,CAAC,CAAC;MAClC,IAAIpE,IAAI,CAACD,OAAO,EAAE;QAChBxF,OAAO,CAACwF,OAAO,CAACC,IAAI,CAACzF,OAAO,CAAC;QAC7B;QACAkF,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLlF,OAAO,CAAC8F,KAAK,CAACL,IAAI,CAACzF,OAAO,CAAC;MAC7B;IACF,CAAC,CAAC,OAAO8F,KAAK,EAAE;MACd9F,OAAO,CAAC8F,KAAK,CAAC,iCAAiC,CAAC;IAClD,CAAC,SAAS;MACR9B,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAED;EACA,IAAI,CAAC+D,IAAI,EAAE;IACT,oBACEnC,OAAA;MAAKqH,SAAS,EAAC,qGAAqG;MAAAC,QAAA,eAClHtH,OAAA;QAAKqH,SAAS,EAAC,2GAA2G;QAAAC,QAAA,gBACxHtH,OAAA;UAAKqH,SAAS,EAAC,kFAAkF;UAAAC,QAAA,eAC/FtH,OAAA;YAAKqH,SAAS,EAAC,sBAAsB;YAACE,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAF,QAAA,eAC3EtH,OAAA;cAAMyH,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,8JAA8J;cAACC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/H,OAAA;UAAIqH,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAuB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClF/H,OAAA;UAAGqH,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAiE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvG/H,OAAA;UACEqH,SAAS,EAAC,mNAAmN;UAC7NW,OAAO,EAAEA,CAAA,KAAMxG,QAAQ,CAAC,QAAQ,CAAE;UAAA8F,QAAA,EACnC;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAOnH,QAAQ,gBACbZ,OAAA;IAAKqH,SAAS,EAAC,oEAAoE;IAAAC,QAAA,GAEhF7F,IAAI,KAAK,cAAc,iBACtBzB,OAAA,CAAC1B,YAAY;MACXsC,QAAQ,EAAEA,QAAS;MACnBc,OAAO,EAAEA,OAAQ;MACjBuE,UAAU,EAAEA;IAAW;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF,EAEAtG,IAAI,KAAK,WAAW,KACnBX,SAAS,CAACmC,MAAM,KAAK,CAAC,gBACpBjD,OAAA;MAAKqH,SAAS,EAAC,sGAAsG;MAAAC,QAAA,eACnHtH,OAAA;QAAKqH,SAAS,EAAC,4GAA4G;QAAAC,QAAA,gBACzHtH,OAAA;UAAKqH,SAAS,EAAC,8HAA8H;UAAAC,QAAA,eAC3ItH,OAAA;YAAKqH,SAAS,EAAC,sBAAsB;YAACE,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAF,QAAA,eAC3EtH,OAAA;cAAMyH,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,mNAAmN;cAACC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/H,OAAA;UAAIqH,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9E/H,OAAA;UAAGqH,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EAAC;QAE3D;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ/H,OAAA;UAAIqH,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBACrDtH,OAAA;YAAAsH,QAAA,EAAI;UAA4C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrD/H,OAAA;YAAAsH,QAAA,EAAI;UAA4B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrC/H,OAAA;YAAAsH,QAAA,EAAI;UAA6B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACL/H,OAAA;UAAKqH,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtH,OAAA;YACEgI,OAAO,EAAEpB,mBAAoB;YAC7BS,SAAS,EAAC,iNAAiN;YAAAC,QAAA,EAC5N;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/H,OAAA;YACEgI,OAAO,EAAEA,CAAA,KAAM;cACbtF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;cAC1CF,WAAW,CAAC,CAAC;YACf,CAAE;YACF4E,SAAS,EAAC,2MAA2M;YAAAC,QAAA,EACtN;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/H,OAAA;YACEgI,OAAO,EAAEA,CAAA,KAAMxG,QAAQ,CAAC,YAAY,CAAE;YACtC6F,SAAS,EAAC,2MAA2M;YAAAC,QAAA,EACtN;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAEN/H,OAAA;MAAKqH,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBAErEtH,OAAA,CAAChC,MAAM,CAACiK,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9Bf,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eAEnFtH,OAAA;UAAKqH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BtH,OAAA;YAAKqH,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAErDtH,OAAA;cAAKqH,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CtH,OAAA,CAACP,OAAO;gBAAC4H,SAAS,EAAC;cAA0B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChD/H,OAAA;gBAAAsH,QAAA,eACEtH,OAAA;kBAAIqH,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAE,CAAA1G,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsD,IAAI,KAAI;gBAAM;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/H,OAAA,CAACnB,SAAS;cACRqE,QAAQ,EAAE,CAAAtC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsC,QAAQ,KAAI,CAAE;cAClCoF,QAAQ,EAAEA,CAAA,KAAM;gBACdxG,SAAS,CAAC,IAAI,CAAC;gBACf0B,eAAe,CAAC,CAAC;cACnB,CAAE;cACF+E,QAAQ,EAAE,CAAC1G,MAAO;cAClB2G,WAAW,EAAE,IAAK;cAClBC,gBAAgB,EAAE;YAAI;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb/H,OAAA;QAAKqH,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpCtH,OAAA,CAAChC,MAAM,CAACiK,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BM,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAAArB,QAAA,EAE1BxG,SAAS,CAACE,qBAAqB,CAAC,gBAC/BhB,OAAA,CAACpB,YAAY;YACXqF,QAAQ,EAAE;cACR,GAAGnD,SAAS,CAACE,qBAAqB,CAAC;cACnC4H,IAAI,EAAE,EAAAzI,qBAAA,GAAAW,SAAS,CAACE,qBAAqB,CAAC,cAAAb,qBAAA,uBAAhCA,qBAAA,CAAkC4D,UAAU,MAAK,SAAS,GAAG,KAAK,GAClE,EAAA3D,sBAAA,GAAAU,SAAS,CAACE,qBAAqB,CAAC,cAAAZ,sBAAA,uBAAhCA,sBAAA,CAAkC2D,UAAU,MAAK,WAAW,IAC5D,EAAA1D,sBAAA,GAAAS,SAAS,CAACE,qBAAqB,CAAC,cAAAX,sBAAA,uBAAhCA,sBAAA,CAAkC0D,UAAU,MAAK,mBAAmB,GAAG,MAAM,GAC7E,CAAAzD,sBAAA,GAAAQ,SAAS,CAACE,qBAAqB,CAAC,cAAAV,sBAAA,eAAhCA,sBAAA,CAAkCwF,QAAQ,GAAG,OAAO,GAAG,KAAK;cAClEX,OAAO,EAAE,CAAA5E,sBAAA,GAAAO,SAAS,CAACE,qBAAqB,CAAC,cAAAT,sBAAA,eAAhCA,sBAAA,CAAkC4E,OAAO,GAChD0D,MAAM,CAACC,MAAM,CAAChI,SAAS,CAACE,qBAAqB,CAAC,CAACmE,OAAO,CAAC,CAAC4D,MAAM,CAACC,MAAM,IAAIA,MAAM,IAAIA,MAAM,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG;YACxG,CAAE;YACJC,cAAc,EAAElI,qBAAqB,GAAG,CAAE;YAC1CmI,cAAc,EAAErI,SAAS,CAACmC,MAAO;YACjCmG,cAAc,EAAElI,eAAe,CAACF,qBAAqB,CAAE;YACvDqI,cAAc,EAAGC,MAAM,IACrBnI,kBAAkB,CAAC;cACjB,GAAGD,eAAe;cAClB,CAACF,qBAAqB,GAAGsI;YAC3B,CAAC,CACF;YACDC,MAAM,EAAEA,CAAA,KAAM;cACZ,IAAIvI,qBAAqB,KAAKF,SAAS,CAACmC,MAAM,GAAG,CAAC,EAAE;gBAClDO,eAAe,CAAC,CAAC;cACnB,CAAC,MAAM;gBACLvC,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;cACrD;YACF,CAAE;YACAwI,UAAU,EAAEA,CAAA,KAAM;cAChB,IAAIxI,qBAAqB,GAAG,CAAC,EAAE;gBAC7BC,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;cACrD;YACF,CAAE;YACFyI,aAAa,EAAE9H,WAAY;YAC3B+H,cAAc,EAAE1I,qBAAqB,KAAKF,SAAS,CAACmC,MAAM,GAAG;UAAE;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,gBAEF/H,OAAA;YAAKqH,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzEtH,OAAA;cAAGqH,SAAS,EAAC;YAAwD;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1E/H,OAAA;cAAIqH,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAsB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnF/H,OAAA;cAAGqH,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAkE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClG/H,OAAA;cACEgI,OAAO,EAAEA,CAAA,KAAMvC,MAAM,CAACkE,QAAQ,CAACC,MAAM,CAAC,CAAE;cACxCvC,SAAS,EAAC,oFAAoF;cAAAC,QAAA,EAC/F;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN/H,OAAA,CAAClB,gBAAgB;QACf2K,aAAa,EAAE9H,WAAY;QAC3BkI,OAAO,EAAEA,CAAA,KAAM,CAAC;MAAE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,CACF,EAEAtG,IAAI,KAAK,QAAQ,iBAChBzB,OAAA;MAAKqH,SAAS,EAAC,6EAA6E;MAAAC,QAAA,GACzFlG,MAAM,CAACiE,OAAO,KAAK,MAAM,iBAAIrF,OAAA,CAACvB,QAAQ;QAAC4D,KAAK,EAAEA,KAAM;QAACC,MAAM,EAAEA;MAAO;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAExE/H,OAAA;QAAKqH,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCtH,OAAA;UAAKqH,SAAS,EAAC,+FAA+F;UAAAC,QAAA,gBAE5GtH,OAAA;YAAKqH,SAAS,EAAG,mCACfjG,MAAM,CAACiE,OAAO,KAAK,MAAM,GACrB,sEAAsE,GACtE,oEACL,EAAE;YAAAiC,QAAA,eACDtH,OAAA;cAAKqH,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBtH,OAAA;gBAAKqH,SAAS,EAAG,iFACfjG,MAAM,CAACiE,OAAO,KAAK,MAAM,GACrB,iDAAiD,GACjD,gDACL,EAAE;gBAAAiC,QAAA,eACDtH,OAAA;kBACE8J,GAAG,EAAE1I,MAAM,CAACiE,OAAO,KAAK,MAAM,GAAG9G,IAAI,GAAGC,IAAK;kBAC7CuL,GAAG,EAAE3I,MAAM,CAACiE,OAAQ;kBACpBgC,SAAS,EAAC;gBAA0B;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/H,OAAA;gBAAIqH,SAAS,EAAG,2CACdjG,MAAM,CAACiE,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;gBAAAiC,QAAA,EACAlG,MAAM,CAACiE,OAAO,KAAK,MAAM,GAAG,iBAAiB,GAAG;cAAe;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACL/H,OAAA;gBAAGqH,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAC/ElG,MAAM,CAACiE,OAAO,KAAK,MAAM,GACtB,+CAA+C,GAC/C;cAAgD;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/H,OAAA;YAAKqH,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBtH,OAAA;cAAKqH,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEzDtH,OAAA;gBAAKqH,SAAS,EAAC,yKAAyK;gBAAAC,QAAA,gBACtLtH,OAAA;kBAAKqH,SAAS,EAAC;gBAAqI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3J/H,OAAA;kBAAKqH,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCtH,OAAA;oBAAKqH,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,GACnE0C,IAAI,CAACC,KAAK,CAAE,CAAC,EAAAzJ,qBAAA,GAAAY,MAAM,CAACwD,cAAc,cAAApE,qBAAA,uBAArBA,qBAAA,CAAuByC,MAAM,KAAI,CAAC,IAAInC,SAAS,CAACmC,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;kBAAA;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN/H,OAAA;oBAAKqH,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,EAAC;kBAAU;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN/H,OAAA;gBAAKqH,SAAS,EAAC,8KAA8K;gBAAAC,QAAA,gBAC3LtH,OAAA;kBAAKqH,SAAS,EAAC;gBAAwI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9J/H,OAAA;kBAAKqH,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCtH,OAAA;oBAAKqH,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,GACtE,EAAA7G,sBAAA,GAAAW,MAAM,CAACwD,cAAc,cAAAnE,sBAAA,uBAArBA,sBAAA,CAAuBwC,MAAM,KAAI,CAAC,EAAC,GAAC,EAACnC,SAAS,CAACmC,MAAM;kBAAA;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACN/H,OAAA;oBAAKqH,SAAS,EAAC,gEAAgE;oBAAAC,QAAA,EAAC;kBAAO;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN/H,OAAA;gBAAKqH,SAAS,EAAG,qGACfjG,MAAM,CAACiE,OAAO,KAAK,MAAM,GACrB,4EAA4E,GAC5E,yEACL,EAAE;gBAAAiC,QAAA,gBACDtH,OAAA;kBAAKqH,SAAS,EAAG,wGACfjG,MAAM,CAACiE,OAAO,KAAK,MAAM,GAAG,oBAAoB,GAAG,kBACpD;gBAAiB;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzB/H,OAAA;kBAAKqH,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCtH,OAAA;oBAAKqH,SAAS,EAAG,2CACfjG,MAAM,CAACiE,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;oBAAAiC,QAAA,EACAlG,MAAM,CAACiE,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG;kBAAO;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACN/H,OAAA;oBAAKqH,SAAS,EAAG,8CACfjG,MAAM,CAACiE,OAAO,KAAK,MAAM,GAAG,qBAAqB,GAAG,mBACrD,EAAE;oBAAAiC,QAAA,EACAlG,MAAM,CAACiE,OAAO,KAAK,MAAM,GAAG,UAAU,GAAI,QAAOzE,QAAQ,CAAC0E,YAAa;kBAAC;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/H,OAAA;cAAKqH,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBtH,OAAA;gBAAKqH,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDtH,OAAA;kBAAKqH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BtH,OAAA;oBAAIqH,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/E/H,OAAA;oBAAGqH,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACN/H,OAAA;kBAAKqH,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBtH,OAAA;oBAAKqH,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChFtH,OAAA;sBACEqH,SAAS,EAAG,uFACVjG,MAAM,CAACiE,OAAO,KAAK,MAAM,GACrB,6DAA6D,GAC7D,2DACL,EAAE;sBACH6E,KAAK,EAAE;wBAAE7H,KAAK,EAAG,GAAG,CAAC,EAAA3B,sBAAA,GAAAU,MAAM,CAACwD,cAAc,cAAAlE,sBAAA,uBAArBA,sBAAA,CAAuBuC,MAAM,KAAI,CAAC,IAAInC,SAAS,CAACmC,MAAM,GAAI,GAAI;sBAAG,CAAE;sBAAAqE,QAAA,eAExFtH,OAAA;wBAAKqH,SAAS,EAAC;sBAAgE;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN/H,OAAA;oBAAKqH,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDtH,OAAA;sBAAMqH,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAE;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9D/H,OAAA;sBAAMqH,SAAS,EAAG,qCAChBjG,MAAM,CAACiE,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;sBAAAiC,QAAA,GACA0C,IAAI,CAACC,KAAK,CAAE,CAAC,EAAAtJ,sBAAA,GAAAS,MAAM,CAACwD,cAAc,cAAAjE,sBAAA,uBAArBA,sBAAA,CAAuBsC,MAAM,KAAI,CAAC,IAAInC,SAAS,CAACmC,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;oBAAA;sBAAA2E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP/H,OAAA;sBAAMqH,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/H,OAAA;cAAKqH,SAAS,EAAC,gDAAgD;cAAAC,QAAA,eAC7DtH,OAAA;gBACEqH,SAAS,EAAC,sPAAsP;gBAChQW,OAAO,EAAEA,CAAA,KAAMtG,OAAO,CAAC,QAAQ,CAAE;gBAAA4F,QAAA,gBAEjCtH,OAAA;kBAAKqH,SAAS,EAAC;gBAAkI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxJ/H,OAAA;kBAAMqH,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAtG,IAAI,KAAK,QAAQ,iBAChBzB,OAAA;MAAKqH,SAAS,EAAC,6EAA6E;MAAAC,QAAA,eAC1FtH,OAAA;QAAKqH,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAErCtH,OAAA;UAAKqH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BtH,OAAA;YAAKqH,SAAS,EAAC,kFAAkF;YAAAC,QAAA,gBAC/FtH,OAAA;cAAIqH,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAAC;YAEnH;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/H,OAAA;cAAGqH,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA8B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/H,OAAA;UAAKqH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BxG,SAAS,CAACqJ,GAAG,CAAC,CAAClG,QAAQ,EAAEmG,KAAK,KAAK;YAClC,MAAMhG,aAAa,GAAGH,QAAQ,CAACF,UAAU,KAAK,SAAS,GACnDE,QAAQ,CAACI,aAAa,GACtBJ,QAAQ,CAACG,aAAa;YAC1B,MAAMM,SAAS,GAAGN,aAAa,KAAKlD,eAAe,CAACkJ,KAAK,CAAC;YAC1D,MAAM9F,UAAU,GAAGpD,eAAe,CAACkJ,KAAK,CAAC;YAEzC,oBACEpK,OAAA;cAEEqH,SAAS,EAAC,oDAAoD;cAC9D6C,KAAK,EAAE;gBACLG,eAAe,EAAE3F,SAAS,GAAG,SAAS,GAAG,SAAS;gBAClD4F,WAAW,EAAE5F,SAAS,GAAG,SAAS,GAAG;cACvC,CAAE;cAAA4C,QAAA,gBAGFtH,OAAA;gBAAKqH,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBtH,OAAA;kBAAKqH,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCtH,OAAA;oBAAKqH,SAAS,EAAC,iHAAiH;oBAAAC,QAAA,EAC7H8C,KAAK,GAAG;kBAAC;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACN/H,OAAA;oBAAKqH,SAAS,EAAC,QAAQ;oBAAAC,QAAA,eACrBtH,OAAA;sBAAGqH,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAErD,QAAQ,CAACC;oBAAI;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN/H,OAAA;gBAAKqH,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBtH,OAAA;kBAAMqH,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3E/H,OAAA;kBAAMqH,SAAS,EAAG,eAAc3C,SAAS,GAAG,gBAAgB,GAAG,cAAe,EAAE;kBAAA4C,QAAA,EAC7ErD,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC7BE,QAAQ,CAACkB,OAAO,IAAIb,UAAU,IAAIL,QAAQ,CAACkB,OAAO,CAACb,UAAU,CAAC,IAAKA,UAAU,IAAI,cAAc,GAChGA,UAAU,IAAI;gBAAc;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,EACNrD,SAAS,gBACR1E,OAAA;kBAAMqH,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAElE/H,OAAA;kBAAMqH,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAChE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGL,CAACrD,SAAS,iBACT1E,OAAA;gBAAKqH,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBtH,OAAA;kBAAMqH,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9E/H,OAAA;kBAAMqH,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACzCrD,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC7BE,QAAQ,CAACkB,OAAO,IAAIlB,QAAQ,CAACkB,OAAO,CAAClB,QAAQ,CAACI,aAAa,CAAC,IAAKJ,QAAQ,CAACI,aAAa,GACvFJ,QAAQ,CAACG,aAAa,IAAIH,QAAQ,CAACI;gBAAc;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACP/H,OAAA;kBAAMqH,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CACN,EAGA,CAACrD,SAAS,iBACT1E,OAAA;gBAAKqH,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBtH,OAAA;kBACEqH,SAAS,EAAC,iKAAiK;kBAC3KW,OAAO,EAAEA,CAAA,KAAM;oBACbtF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEsB,QAAQ,CAACC,IAAI,CAAC;oBACvD2B,gBAAgB,CACd5B,QAAQ,CAACC,IAAI,EACbD,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC5BE,QAAQ,CAACkB,OAAO,IAAIlB,QAAQ,CAACkB,OAAO,CAAClB,QAAQ,CAACI,aAAa,CAAC,IAAKJ,QAAQ,CAACI,aAAa,GACvFJ,QAAQ,CAACG,aAAa,IAAIH,QAAQ,CAACI,aAAc,EACtDJ,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC5BE,QAAQ,CAACkB,OAAO,IAAIlB,QAAQ,CAACkB,OAAO,CAACb,UAAU,CAAC,IAAKA,UAAU,IAAI,cAAc,GAClFA,UAAU,IAAI,cAAc,EAChCL,QAAQ,CAACsG,KACX,CAAC;kBACH,CAAE;kBAAAjD,QAAA,gBAEFtH,OAAA;oBAAAsH,QAAA,EAAM;kBAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACf/H,OAAA;oBAAAsH,QAAA,EAAM;kBAAe;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,EAGAxF,YAAY,CAAC0B,QAAQ,CAACC,IAAI,CAAC,iBAC1BlE,OAAA;gBAAKqH,SAAS,EAAC,4FAA4F;gBAAAC,QAAA,gBACzGtH,OAAA;kBAAKqH,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCtH,OAAA;oBAAMqH,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtD/H,OAAA;oBAAIqH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAElD;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,EAGL,CAAC9D,QAAQ,CAACsG,KAAK,IAAItG,QAAQ,CAAC6B,QAAQ,kBACnC9F,OAAA;kBAAKqH,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,gBACjEtH,OAAA;oBAAKqH,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eACrCtH,OAAA;sBAAMqH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAqB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC,eACN/H,OAAA;oBAAKqH,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,eAClCtH,OAAA;sBACE8J,GAAG,EAAE7F,QAAQ,CAACsG,KAAK,IAAItG,QAAQ,CAAC6B,QAAS;sBACzCiE,GAAG,EAAC,kBAAkB;sBACtB1C,SAAS,EAAC,mEAAmE;sBAC7E6C,KAAK,EAAE;wBAAEM,QAAQ,EAAE;sBAAQ;oBAAE;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAED/H,OAAA;kBAAKqH,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,eAC3EtH,OAAA,CAACH,eAAe;oBAAC4K,IAAI,EAAElI,YAAY,CAAC0B,QAAQ,CAACC,IAAI;kBAAE;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA,GAvGIqC,KAAK;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwGP,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/H,OAAA;UAAKqH,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAC1EtH,OAAA;YACEqH,SAAS,EAAC,sPAAsP;YAChQW,OAAO,EAAEA,CAAA,KAAMtG,OAAO,CAAC,QAAQ,CAAE;YAAA4F,QAAA,gBAEjCtH,OAAA;cAAKqH,SAAS,EAAC;YAAkI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxJ/H,OAAA;cAAMqH,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAET/H,OAAA;YACEqH,SAAS,EAAC,0PAA0P;YACpQW,OAAO,EAAEA,CAAA,KAAM;cACb;cACAtG,OAAO,CAAC,cAAc,CAAC;cACvBT,wBAAwB,CAAC,CAAC,CAAC;cAC3BE,kBAAkB,CAAC,CAAC,CAAC,CAAC;cACtBE,SAAS,CAAC,CAAC,CAAC,CAAC;cACbS,SAAS,CAAC,KAAK,CAAC;cAChBF,cAAc,CAAC,CAAAhB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsC,QAAQ,KAAI,CAAC,CAAC;cACvCV,eAAe,CAAC,CAAC,CAAC,CAAC;YACrB,CAAE;YAAA8E,QAAA,gBAEFtH,OAAA;cAAKqH,SAAS,EAAC;YAAkI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxJ/H,OAAA;cAAMqH,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC,GACJ,IAAI;AACV;AAAC7H,EAAA,CA1uBQD,SAAS;EAAA,QAMDlC,SAAS,EACPH,WAAW,EACXE,WAAW,EAMXD,WAAW,EAEFa,aAAa;AAAA;AAAAgM,EAAA,GAhBhCzK,SAAS;AA4uBlB,eAAeA,SAAS;AAAC,IAAAyK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}