import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { message } from 'antd';
import { getExamById } from '../../../apicalls/exams';
import { addReport } from '../../../apicalls/reports';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import { chatWithChatGPTToGetAns } from '../../../apicalls/chat';
import QuizRenderer from '../../../components/QuizRenderer';

const QuizPlay = () => {
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [secondsLeft, setSecondsLeft] = useState(0);
  const [timeUp, setTimeUp] = useState(false);
  const [intervalId, setIntervalId] = useState(null);
  
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  const getExamData = async () => {
    try {
      dispatch(ShowLoading());
      const response = await getExamById({ examId: id });
      dispatch(HideLoading());

      if (response.success) {
        setQuestions(response.data?.questions || []);
        setExamData(response.data);
        setSecondsLeft(response.data?.duration || 0);
      } else {
        message.error(response.message);
        navigate('/user/quiz');
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
      navigate('/user/quiz');
    }
  };

  const checkFreeTextAnswers = async (payload) => {
    if (!payload.length) return [];
    const { data } = await chatWithChatGPTToGetAns(payload);
    return data;
  };

  const calculateResult = useCallback(async () => {
    try {
      if (!user || !user._id) {
        message.error("User not found. Please log in again.");
        navigate("/login");
        return;
      }

      dispatch(ShowLoading());

      const freeTextPayload = [];
      questions.forEach((q, idx) => {
        if (q.type === "fill" || q.answerType === "Free Text" || q.answerType === "Fill in the Blank") {
          freeTextPayload.push({
            question: q.name,
            expectedAnswer: q.correctAnswer || q.correctOption,
            userAnswer: selectedOptions[idx] || "",
          });
        }
      });

      const gptResults = await checkFreeTextAnswers(freeTextPayload);
      const gptMap = {};

      gptResults.forEach((r) => {
        if (r.result && typeof r.result.isCorrect === "boolean") {
          gptMap[r.question] = r.result;
        } else if (typeof r.isCorrect === "boolean") {
          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || "" };
        }
      });

      const correctAnswers = [];
      const wrongAnswers = [];

      questions.forEach((q, idx) => {
        const userAnswerKey = selectedOptions[idx] || "";

        if (q.type === "fill" || q.answerType === "Free Text" || q.answerType === "Fill in the Blank") {
          const { isCorrect = false, reason = "" } = gptMap[q.name] || {};
          const enriched = { ...q, userAnswer: userAnswerKey, reason };

          if (isCorrect) {
            correctAnswers.push(enriched);
          } else {
            wrongAnswers.push(enriched);
          }
        } else if (q.type === "mcq" || q.answerType === "Options") {
          const correctKey = q.correctOption || q.correctAnswer;
          const isCorrect = correctKey === userAnswerKey;
          const enriched = { ...q, userAnswer: userAnswerKey };

          if (isCorrect) {
            correctAnswers.push(enriched);
          } else {
            wrongAnswers.push(enriched);
          }
        }
      });

      const verdict = correctAnswers.length >= examData.passingMarks ? "Pass" : "Fail";
      const tempResult = { correctAnswers, wrongAnswers, verdict };

      const response = await addReport({
        exam: id,
        result: tempResult,
        user: user._id,
      });

      if (response.success) {
        navigate(`/quiz/${id}/result`, { state: { result: tempResult } });
      } else {
        message.error(response.message);
      }
      dispatch(HideLoading());

    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);

  const startTimer = useCallback(() => {
    const totalSeconds = examData?.duration || 0;
    setSecondsLeft(totalSeconds);

    const newIntervalId = setInterval(() => {
      setSecondsLeft((prevSeconds) => {
        if (prevSeconds > 0) {
          return prevSeconds - 1;
        } else {
          setTimeUp(true);
          return 0;
        }
      });
    }, 1000);
    setIntervalId(newIntervalId);
  }, [examData]);

  useEffect(() => {
    if (timeUp) {
      clearInterval(intervalId);
      calculateResult();
    }
  }, [timeUp, intervalId, calculateResult]);

  useEffect(() => {
    if (id) {
      getExamData();
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Add quiz-fullscreen class for fullscreen experience
  useEffect(() => {
    document.body.classList.add('quiz-fullscreen');

    return () => {
      document.body.classList.remove('quiz-fullscreen');
    };
  }, []);

  useEffect(() => {
    if (examData && questions.length > 0) {
      startTimer();
    }
  }, [examData, questions]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [intervalId]);

  if (!examData || questions.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading quiz questions...</p>
        </div>
      </div>
    );
  }

  return (
    <QuizRenderer
      question={questions[selectedQuestionIndex]}
      questionIndex={selectedQuestionIndex}
      totalQuestions={questions.length}
      selectedAnswer={selectedOptions[selectedQuestionIndex]}
      onAnswerChange={(answer) =>
        setSelectedOptions({
          ...selectedOptions,
          [selectedQuestionIndex]: answer,
        })
      }
      timeLeft={secondsLeft}
      username={user?.name || "Student"}
      onNext={() => {
        if (selectedQuestionIndex === questions.length - 1) {
          calculateResult();
        } else {
          setSelectedQuestionIndex(selectedQuestionIndex + 1);
        }
      }}
      onPrevious={() => setSelectedQuestionIndex(selectedQuestionIndex - 1)}
    />
  );
};

export default QuizPlay;
