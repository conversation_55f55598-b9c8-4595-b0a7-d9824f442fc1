{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { chatWithChatGPTToGetAns } from '../../../apicalls/chat';\nimport QuizRenderer from '../../../components/QuizRenderer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const getExamData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({\n        examId: id\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        var _response$data, _response$data2;\n        setQuestions(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.questions) || []);\n        setExamData(response.data);\n        setSecondsLeft(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.duration) || 0);\n      } else {\n        message.error(response.message);\n        navigate('/user/quiz');\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n      navigate('/user/quiz');\n    }\n  };\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      questions.forEach((q, idx) => {\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        } else if (q.type === \"mcq\" || q.answerType === \"Options\") {\n          const correctKey = q.correctOption || q.correctAnswer;\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        }\n      });\n      const verdict = correctAnswers.length >= examData.passingMarks ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers,\n        wrongAnswers,\n        verdict\n      };\n      const response = await addReport({\n        exam: id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        navigate(`/quiz/${id}/result`, {\n          state: {\n            result: tempResult\n          }\n        });\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);\n  const startTimer = useCallback(() => {\n    const totalSeconds = (examData === null || examData === void 0 ? void 0 : examData.duration) || 0;\n    setSecondsLeft(totalSeconds);\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  }, [examData]);\n  useEffect(() => {\n    if (timeUp) {\n      clearInterval(intervalId);\n      calculateResult();\n    }\n  }, [timeUp, intervalId, calculateResult]);\n  useEffect(() => {\n    if (id) {\n      getExamData();\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Add quiz-fullscreen class for fullscreen experience\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  useEffect(() => {\n    if (examData && questions.length > 0) {\n      startTimer();\n    }\n  }, [examData, questions]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [intervalId]);\n  if (!examData || questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading quiz questions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(QuizRenderer, {\n    question: questions[selectedQuestionIndex],\n    questionIndex: selectedQuestionIndex,\n    totalQuestions: questions.length,\n    selectedAnswer: selectedOptions[selectedQuestionIndex],\n    onAnswerChange: answer => setSelectedOptions({\n      ...selectedOptions,\n      [selectedQuestionIndex]: answer\n    }),\n    timeLeft: secondsLeft,\n    username: (user === null || user === void 0 ? void 0 : user.name) || \"Student\",\n    onNext: () => {\n      if (selectedQuestionIndex === questions.length - 1) {\n        calculateResult();\n      } else {\n        setSelectedQuestionIndex(selectedQuestionIndex + 1);\n      }\n    },\n    onPrevious: () => setSelectedQuestionIndex(selectedQuestionIndex - 1)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"jLUOroNOwm+wt2L5jRclI/4F/wI=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "useDispatch", "useSelector", "message", "getExamById", "addReport", "HideLoading", "ShowLoading", "chatWithChatGPTToGetAns", "Quiz<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "QuizPlay", "_s", "examData", "setExamData", "questions", "setQuestions", "selectedQuestionIndex", "setSelectedQuestionIndex", "selectedOptions", "setSelectedOptions", "secondsLeft", "setSecondsLeft", "timeUp", "setTimeUp", "intervalId", "setIntervalId", "id", "navigate", "dispatch", "user", "state", "getExamData", "response", "examId", "success", "_response$data", "_response$data2", "data", "duration", "error", "checkFreeTextAnswers", "payload", "length", "calculateResult", "_id", "freeTextPayload", "for<PERSON>ach", "q", "idx", "type", "answerType", "push", "question", "name", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "userAnswer", "gptResults", "gptMap", "r", "result", "isCorrect", "reason", "correctAnswers", "wrongAnswers", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "verdict", "passingMarks", "tempResult", "exam", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "document", "body", "classList", "add", "remove", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "answer", "timeLeft", "username", "onNext", "onPrevious", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { chatWithChatGPTToGetAns } from '../../../apicalls/chat';\nimport QuizRenderer from '../../../components/QuizRenderer';\n\nconst QuizPlay = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  \n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  const getExamData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({ examId: id });\n      dispatch(HideLoading());\n\n      if (response.success) {\n        setQuestions(response.data?.questions || []);\n        setExamData(response.data);\n        setSecondsLeft(response.data?.duration || 0);\n      } else {\n        message.error(response.message);\n        navigate('/user/quiz');\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n      navigate('/user/quiz');\n    }\n  };\n\n  const checkFreeTextAnswers = async (payload) => {\n    if (!payload.length) return [];\n    const { data } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n\n  const calculateResult = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n\n      dispatch(ShowLoading());\n\n      const freeTextPayload = [];\n      questions.forEach((q, idx) => {\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\",\n          });\n        }\n      });\n\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n\n      gptResults.forEach((r) => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\n        }\n      });\n\n      const correctAnswers = [];\n      const wrongAnswers = [];\n\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        } else if (q.type === \"mcq\" || q.answerType === \"Options\") {\n          const correctKey = q.correctOption || q.correctAnswer;\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = { ...q, userAnswer: userAnswerKey };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        }\n      });\n\n      const verdict = correctAnswers.length >= examData.passingMarks ? \"Pass\" : \"Fail\";\n      const tempResult = { correctAnswers, wrongAnswers, verdict };\n\n      const response = await addReport({\n        exam: id,\n        result: tempResult,\n        user: user._id,\n      });\n\n      if (response.success) {\n        navigate(`/quiz/${id}/result`, { state: { result: tempResult } });\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);\n\n  const startTimer = useCallback(() => {\n    const totalSeconds = examData?.duration || 0;\n    setSecondsLeft(totalSeconds);\n\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft((prevSeconds) => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  }, [examData]);\n\n  useEffect(() => {\n    if (timeUp) {\n      clearInterval(intervalId);\n      calculateResult();\n    }\n  }, [timeUp, intervalId, calculateResult]);\n\n  useEffect(() => {\n    if (id) {\n      getExamData();\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Add quiz-fullscreen class for fullscreen experience\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  useEffect(() => {\n    if (examData && questions.length > 0) {\n      startTimer();\n    }\n  }, [examData, questions]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [intervalId]);\n\n  if (!examData || questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading quiz questions...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <QuizRenderer\n      question={questions[selectedQuestionIndex]}\n      questionIndex={selectedQuestionIndex}\n      totalQuestions={questions.length}\n      selectedAnswer={selectedOptions[selectedQuestionIndex]}\n      onAnswerChange={(answer) =>\n        setSelectedOptions({\n          ...selectedOptions,\n          [selectedQuestionIndex]: answer,\n        })\n      }\n      timeLeft={secondsLeft}\n      username={user?.name || \"Student\"}\n      onNext={() => {\n        if (selectedQuestionIndex === questions.length - 1) {\n          calculateResult();\n        } else {\n          setSelectedQuestionIndex(selectedQuestionIndex + 1);\n        }\n      }}\n      onPrevious={() => setSelectedQuestionIndex(selectedQuestionIndex - 1)}\n    />\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,OAAOC,YAAY,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAElD,MAAM;IAAEgC;EAAG,CAAC,GAAG7B,SAAS,CAAC,CAAC;EAC1B,MAAM8B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8B;EAAK,CAAC,GAAG7B,WAAW,CAAE8B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAME,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFH,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM2B,QAAQ,GAAG,MAAM9B,WAAW,CAAC;QAAE+B,MAAM,EAAEP;MAAG,CAAC,CAAC;MAClDE,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAI4B,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAC,cAAA,EAAAC,eAAA;QACpBrB,YAAY,CAAC,EAAAoB,cAAA,GAAAH,QAAQ,CAACK,IAAI,cAAAF,cAAA,uBAAbA,cAAA,CAAerB,SAAS,KAAI,EAAE,CAAC;QAC5CD,WAAW,CAACmB,QAAQ,CAACK,IAAI,CAAC;QAC1BhB,cAAc,CAAC,EAAAe,eAAA,GAAAJ,QAAQ,CAACK,IAAI,cAAAD,eAAA,uBAAbA,eAAA,CAAeE,QAAQ,KAAI,CAAC,CAAC;MAC9C,CAAC,MAAM;QACLrC,OAAO,CAACsC,KAAK,CAACP,QAAQ,CAAC/B,OAAO,CAAC;QAC/B0B,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdX,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MACvBH,OAAO,CAACsC,KAAK,CAACA,KAAK,CAACtC,OAAO,CAAC;MAC5B0B,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMa,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAACC,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAEL;IAAK,CAAC,GAAG,MAAM/B,uBAAuB,CAACmC,OAAO,CAAC;IACvD,OAAOJ,IAAI;EACb,CAAC;EAED,MAAMM,eAAe,GAAG/C,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF,IAAI,CAACiC,IAAI,IAAI,CAACA,IAAI,CAACe,GAAG,EAAE;QACtB3C,OAAO,CAACsC,KAAK,CAAC,sCAAsC,CAAC;QACrDZ,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAC,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMwC,eAAe,GAAG,EAAE;MAC1B/B,SAAS,CAACgC,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,IAAID,CAAC,CAACE,IAAI,KAAK,MAAM,IAAIF,CAAC,CAACG,UAAU,KAAK,WAAW,IAAIH,CAAC,CAACG,UAAU,KAAK,mBAAmB,EAAE;UAC7FL,eAAe,CAACM,IAAI,CAAC;YACnBC,QAAQ,EAAEL,CAAC,CAACM,IAAI;YAChBC,cAAc,EAAEP,CAAC,CAACQ,aAAa,IAAIR,CAAC,CAACS,aAAa;YAClDC,UAAU,EAAEvC,eAAe,CAAC8B,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMU,UAAU,GAAG,MAAMlB,oBAAoB,CAACK,eAAe,CAAC;MAC9D,MAAMc,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACZ,OAAO,CAAEc,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACC,MAAM,IAAI,OAAOD,CAAC,CAACC,MAAM,CAACC,SAAS,KAAK,SAAS,EAAE;UACvDH,MAAM,CAACC,CAAC,CAACR,QAAQ,CAAC,GAAGQ,CAAC,CAACC,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAOD,CAAC,CAACE,SAAS,KAAK,SAAS,EAAE;UAC3CH,MAAM,CAACC,CAAC,CAACR,QAAQ,CAAC,GAAG;YAAEU,SAAS,EAAEF,CAAC,CAACE,SAAS;YAAEC,MAAM,EAAEH,CAAC,CAACG,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MAEvBnD,SAAS,CAACgC,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,MAAMkB,aAAa,GAAGhD,eAAe,CAAC8B,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAID,CAAC,CAACE,IAAI,KAAK,MAAM,IAAIF,CAAC,CAACG,UAAU,KAAK,WAAW,IAAIH,CAAC,CAACG,UAAU,KAAK,mBAAmB,EAAE;UAC7F,MAAM;YAAEY,SAAS,GAAG,KAAK;YAAEC,MAAM,GAAG;UAAG,CAAC,GAAGJ,MAAM,CAACZ,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAMc,QAAQ,GAAG;YAAE,GAAGpB,CAAC;YAAEU,UAAU,EAAES,aAAa;YAAEH;UAAO,CAAC;UAE5D,IAAID,SAAS,EAAE;YACbE,cAAc,CAACb,IAAI,CAACgB,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLF,YAAY,CAACd,IAAI,CAACgB,QAAQ,CAAC;UAC7B;QACF,CAAC,MAAM,IAAIpB,CAAC,CAACE,IAAI,KAAK,KAAK,IAAIF,CAAC,CAACG,UAAU,KAAK,SAAS,EAAE;UACzD,MAAMkB,UAAU,GAAGrB,CAAC,CAACS,aAAa,IAAIT,CAAC,CAACQ,aAAa;UACrD,MAAMO,SAAS,GAAGM,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGpB,CAAC;YAAEU,UAAU,EAAES;UAAc,CAAC;UAEpD,IAAIJ,SAAS,EAAE;YACbE,cAAc,CAACb,IAAI,CAACgB,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLF,YAAY,CAACd,IAAI,CAACgB,QAAQ,CAAC;UAC7B;QACF;MACF,CAAC,CAAC;MAEF,MAAME,OAAO,GAAGL,cAAc,CAACtB,MAAM,IAAI9B,QAAQ,CAAC0D,YAAY,GAAG,MAAM,GAAG,MAAM;MAChF,MAAMC,UAAU,GAAG;QAAEP,cAAc;QAAEC,YAAY;QAAEI;MAAQ,CAAC;MAE5D,MAAMrC,QAAQ,GAAG,MAAM7B,SAAS,CAAC;QAC/BqE,IAAI,EAAE9C,EAAE;QACRmC,MAAM,EAAEU,UAAU;QAClB1C,IAAI,EAAEA,IAAI,CAACe;MACb,CAAC,CAAC;MAEF,IAAIZ,QAAQ,CAACE,OAAO,EAAE;QACpBP,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;UAAEI,KAAK,EAAE;YAAE+B,MAAM,EAAEU;UAAW;QAAE,CAAC,CAAC;MACnE,CAAC,MAAM;QACLtE,OAAO,CAACsC,KAAK,CAACP,QAAQ,CAAC/B,OAAO,CAAC;MACjC;MACA2B,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdX,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MACvBH,OAAO,CAACsC,KAAK,CAACA,KAAK,CAACtC,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACa,SAAS,EAAEI,eAAe,EAAEN,QAAQ,EAAEc,EAAE,EAAEG,IAAI,EAAEF,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAExE,MAAM6C,UAAU,GAAG7E,WAAW,CAAC,MAAM;IACnC,MAAM8E,YAAY,GAAG,CAAA9D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE0B,QAAQ,KAAI,CAAC;IAC5CjB,cAAc,CAACqD,YAAY,CAAC;IAE5B,MAAMC,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtCvD,cAAc,CAAEwD,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACLtD,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACRE,aAAa,CAACkD,aAAa,CAAC;EAC9B,CAAC,EAAE,CAAC/D,QAAQ,CAAC,CAAC;EAEdjB,SAAS,CAAC,MAAM;IACd,IAAI2B,MAAM,EAAE;MACVwD,aAAa,CAACtD,UAAU,CAAC;MACzBmB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACrB,MAAM,EAAEE,UAAU,EAAEmB,eAAe,CAAC,CAAC;EAEzChD,SAAS,CAAC,MAAM;IACd,IAAI+B,EAAE,EAAE;MACNK,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACApC,SAAS,CAAC,MAAM;IACdoF,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAE9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENxF,SAAS,CAAC,MAAM;IACd,IAAIiB,QAAQ,IAAIE,SAAS,CAAC4B,MAAM,GAAG,CAAC,EAAE;MACpC+B,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC7D,QAAQ,EAAEE,SAAS,CAAC,CAAC,CAAC,CAAC;;EAE3BnB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAI6B,UAAU,EAAE;QACdsD,aAAa,CAACtD,UAAU,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,IAAI,CAACZ,QAAQ,IAAIE,SAAS,CAAC4B,MAAM,KAAK,CAAC,EAAE;IACvC,oBACEjC,OAAA;MAAK2E,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG5E,OAAA;QAAK2E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5E,OAAA;UAAK2E,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9FhF,OAAA;UAAG2E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhF,OAAA,CAACF,YAAY;IACX6C,QAAQ,EAAEtC,SAAS,CAACE,qBAAqB,CAAE;IAC3C0E,aAAa,EAAE1E,qBAAsB;IACrC2E,cAAc,EAAE7E,SAAS,CAAC4B,MAAO;IACjCkD,cAAc,EAAE1E,eAAe,CAACF,qBAAqB,CAAE;IACvD6E,cAAc,EAAGC,MAAM,IACrB3E,kBAAkB,CAAC;MACjB,GAAGD,eAAe;MAClB,CAACF,qBAAqB,GAAG8E;IAC3B,CAAC,CACF;IACDC,QAAQ,EAAE3E,WAAY;IACtB4E,QAAQ,EAAE,CAAAnE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,KAAI,SAAU;IAClC4C,MAAM,EAAEA,CAAA,KAAM;MACZ,IAAIjF,qBAAqB,KAAKF,SAAS,CAAC4B,MAAM,GAAG,CAAC,EAAE;QAClDC,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM;QACL1B,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;MACrD;IACF,CAAE;IACFkF,UAAU,EAAEA,CAAA,KAAMjF,wBAAwB,CAACD,qBAAqB,GAAG,CAAC;EAAE;IAAAsE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvE,CAAC;AAEN,CAAC;AAAC9E,EAAA,CAnNID,QAAQ;EAAA,QASGb,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAAmG,EAAA,GAZxBzF,QAAQ;AAqNd,eAAeA,QAAQ;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}